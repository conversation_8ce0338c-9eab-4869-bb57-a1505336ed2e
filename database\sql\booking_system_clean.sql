-- =====================================================
-- CARLES TOURISM ONLINE BOOKING SYSTEM DATABASE
-- =====================================================
-- Updated: July 31, 2025
-- Description: Clean and optimized database structure for boat booking system
-- Compatible with: MySQL 5.7+ / MariaDB 10.4+
-- All fields match booking.html form exactly
-- =====================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";
SET FOREIGN_KEY_CHECKS = 0;
SET NAMES utf8mb4;

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS `booking_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `booking_system`;

-- =====================================================
-- CORE TABLES
-- =====================================================

-- Table: destinations
-- Purpose: Store available tour destinations from booking form
CREATE TABLE `destinations` (
  `destination_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`destination_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert destinations matching booking.html form options
INSERT INTO `destinations` (`destination_id`, `name`, `description`, `status`) VALUES
(1, 'Gigantes Island', 'Popular island destination with beautiful beaches', 'active'),
(2, 'Sicogon Island', 'Pristine island with white sand beaches', 'active'),
(3, 'Tumaquin Island', 'Scenic island perfect for day trips', 'active'),
(4, 'Cabugao Gamay Island', 'Small island with crystal clear waters', 'active'),
(5, 'Calagnaan Island', 'Peaceful island destination', 'active');

-- Table: customers
-- Purpose: Store customer information for registered users
CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL UNIQUE,
  `contact_number` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table: boats
-- Purpose: Store boat information for assignments
CREATE TABLE `boats` (
  `boat_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` varchar(50) DEFAULT NULL,
  `capacity` int(11) NOT NULL DEFAULT 25,
  `price_per_day` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `status` varchar(20) DEFAULT 'Available',
  `destination` varchar(255) DEFAULT NULL,
  `availability_status` enum('available','not available','maintenance') DEFAULT 'available',
  `created_at` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`boat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert sample boats
INSERT IGNORE INTO `boats` (`boat_id`, `name`, `type`, `capacity`, `price_per_day`, `description`, `status`, `destination`, `availability_status`) VALUES
(1, 'MV Carles Explorer', 'small', 8, 2500.00, 'Perfect for intimate family trips to nearby islands', 'Available', 'Gigantes Island', 'available'),
(2, 'MV Island Hopper', 'medium', 12, 3500.00, 'Great for group outings and island hopping tours', 'Available', 'Sicogon Island', 'available'),
(3, 'MV Ocean Breeze', 'large', 25, 5000.00, 'Ideal for large groups and corporate events', 'Available', 'Multiple Destinations', 'available'),
(4, 'MV Sea Adventure', 'medium', 15, 4000.00, 'Comfortable boat for medium-sized groups', 'Available', 'Tumaquin Island', 'available'),
(5, 'MV Paradise Cruiser', 'large', 30, 6000.00, 'Luxury boat for special occasions', 'Available', 'Cabugao Gamay', 'available');

-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE IF NOT EXISTS `bookings` (
  `booking_id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) DEFAULT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `suffix` varchar(10) DEFAULT NULL,
  `age` int(11) DEFAULT NULL,
  `sex` varchar(10) DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `emergency_name` varchar(255) DEFAULT NULL,
  `emergency_number` varchar(20) DEFAULT NULL,
  `boat_id` int(11) NOT NULL DEFAULT 1,
  `no_of_pax` int(11) NOT NULL,
  `total_passengers` int(11) DEFAULT NULL,
  `regular_pax` int(11) NOT NULL DEFAULT 0,
  `discounted_pax` int(11) NOT NULL DEFAULT 0,
  `children_pax` int(11) NOT NULL DEFAULT 0,
  `infants_pax` int(11) NOT NULL DEFAULT 0,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `booking_time` datetime NOT NULL DEFAULT current_timestamp(),
  `environmental_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method` varchar(50) NOT NULL,
  `payment_proof` varchar(255) DEFAULT NULL,
  `total` decimal(10,2) NOT NULL,
  `booking_status` enum('pending','confirmed','cancelled','accepted','rejected','verification_pending') NOT NULL DEFAULT 'pending',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `tour_destination` varchar(255) DEFAULT NULL,
  `drop_off_location` varchar(255) DEFAULT NULL,
  `booking_code` varchar(50) NOT NULL,
  `destination_id` int(11) NOT NULL DEFAULT 1,
  `is_today_booking` tinyint(1) DEFAULT 1,
  `passenger_details` text DEFAULT NULL,
  PRIMARY KEY (`booking_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert sample bookings (aligned with booking form fields)
INSERT IGNORE INTO `bookings` (
  `booking_id`, `first_name`, `last_name`, `suffix`, `age`, `sex`, `contact_number`, `email`, `address`,
  `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `total_passengers`,
  `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`,
  `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`,
  `booking_status`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`,
  `is_today_booking`, `passenger_details`
) VALUES
(100, 'Juan', 'Dela Cruz', 'Jr.', 35, 'Male', '09123456789', '<EMAIL>', '123 Main St, Iloilo City',
 'Maria Dela Cruz', '09987654321', 1, 4, 4, 2, 1, 1, 0,
 '2024-08-15 08:00:00', '2024-08-15 17:00:00', '2024-08-10 14:30:00', 120.00, 'GCash', 2620.00,
 'confirmed', 'Gigantes Island', 'Estancia Port', 'BOOK-2024-100', 1, 0,
 '[{"name":"Juan Dela Cruz Jr.","age":35,"sex":"Male","type":"main_booker"},{"name":"Maria Dela Cruz","age":32,"sex":"Female","type":"companion"},{"name":"Jose Dela Cruz","age":60,"sex":"Male","type":"companion"},{"name":"Ana Dela Cruz","age":8,"sex":"Female","type":"companion"}]'),

(101, 'Maria', 'Santos', '', 28, 'Female', '09234567890', '<EMAIL>', '456 Oak Ave, Bacolod City',
 'Pedro Santos', '09876543210', 2, 6, 6, 4, 0, 2, 0,
 '2024-08-20 07:30:00', '2024-08-20 18:00:00', '2024-08-15 10:15:00', 180.00, 'Manual Payment', 3680.00,
 'pending', 'Sicogon Island', 'Carles Port', 'BOOK-2024-101', 2, 0,
 '[{"name":"Maria Santos","age":28,"sex":"Female","type":"main_booker"},{"name":"Pedro Santos","age":30,"sex":"Male","type":"companion"},{"name":"Rosa Santos","age":25,"sex":"Female","type":"companion"},{"name":"Luis Santos","age":27,"sex":"Male","type":"companion"},{"name":"Sofia Santos","age":10,"sex":"Female","type":"companion"},{"name":"Miguel Santos","age":12,"sex":"Male","type":"companion"}]'),

(102, 'Robert', 'Garcia', 'Sr.', 45, 'Male', '09345678901', '<EMAIL>', '789 Pine St, Dumaguete City',
 'Carmen Garcia', '09765432109', 3, 8, 8, 5, 1, 1, 1,
 '2024-08-25 06:00:00', '2024-08-25 19:00:00', '2024-08-20 16:45:00', 240.00, 'GCash', 5240.00,
 'accepted', 'Island Hopping Tour', 'Estancia Port', 'BOOK-2024-102', 1, 0,
 '[{"name":"Robert Garcia Sr.","age":45,"sex":"Male","type":"main_booker"},{"name":"Carmen Garcia","age":42,"sex":"Female","type":"companion"},{"name":"Roberto Garcia","age":20,"sex":"Male","type":"companion"},{"name":"Carla Garcia","age":18,"sex":"Female","type":"companion"},{"name":"Carlos Garcia","age":65,"sex":"Male","type":"companion"},{"name":"Elena Garcia","age":15,"sex":"Female","type":"companion"},{"name":"Nina Garcia","age":11,"sex":"Female","type":"companion"},{"name":"Baby Garcia","age":2,"sex":"Male","type":"companion"}]');

-- --------------------------------------------------------

--
-- Table structure for table `booking_passengers`
--

CREATE TABLE IF NOT EXISTS `booking_passengers` (
  `passenger_id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_id` int(11) NOT NULL,
  `passenger_name` varchar(255) NOT NULL,
  `passenger_age` int(11) NOT NULL,
  `passenger_sex` varchar(10) DEFAULT NULL,
  `passenger_type` enum('main_booker','regular','discounted','children','infants') NOT NULL DEFAULT 'regular',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`passenger_id`),
  KEY `booking_id` (`booking_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert sample passenger data
INSERT IGNORE INTO `booking_passengers` (`booking_id`, `passenger_name`, `passenger_age`, `passenger_sex`, `passenger_type`) VALUES
-- Booking 100 passengers
(100, 'Juan Dela Cruz Jr.', 35, 'Male', 'main_booker'),
(100, 'Maria Dela Cruz', 32, 'Female', 'regular'),
(100, 'Jose Dela Cruz', 60, 'Male', 'discounted'),
(100, 'Ana Dela Cruz', 8, 'Female', 'children'),

-- Booking 101 passengers
(101, 'Maria Santos', 28, 'Female', 'main_booker'),
(101, 'Pedro Santos', 30, 'Male', 'regular'),
(101, 'Rosa Santos', 25, 'Female', 'regular'),
(101, 'Luis Santos', 27, 'Male', 'regular'),
(101, 'Sofia Santos', 10, 'Female', 'children'),
(101, 'Miguel Santos', 12, 'Male', 'children'),

-- Booking 102 passengers
(102, 'Robert Garcia Sr.', 45, 'Male', 'main_booker'),
(102, 'Carmen Garcia', 42, 'Female', 'regular'),
(102, 'Roberto Garcia', 20, 'Male', 'regular'),
(102, 'Carla Garcia', 18, 'Female', 'regular'),
(102, 'Carlos Garcia', 65, 'Male', 'discounted'),
(102, 'Elena Garcia', 15, 'Female', 'regular'),
(102, 'Nina Garcia', 11, 'Female', 'children'),
(102, 'Baby Garcia', 2, 'Male', 'infants');

-- --------------------------------------------------------

--
-- Table structure for table `destinations`
--

CREATE TABLE IF NOT EXISTS `destinations` (
  `destination_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `image_path` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`destination_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert sample destinations
INSERT IGNORE INTO `destinations` (`destination_id`, `name`, `description`, `location`, `status`) VALUES
(1, 'Gigantes Island', 'Famous for its white sand beaches and seafood', 'Carles, Iloilo', 'active'),
(2, 'Sicogon Island', 'Pristine beaches and clear waters', 'Carles, Iloilo', 'active'),
(3, 'Tumaquin Island', 'Beautiful island with pristine beaches', 'Carles, Iloilo', 'active'),
(4, 'Cabugao Gamay Island', 'Small island with beautiful scenery', 'Carles, Iloilo', 'active'),
(5, 'Calagnaan Island', 'Peaceful island destination', 'Carles, Iloilo', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE IF NOT EXISTS `notifications` (
  `notification_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) DEFAULT 'general',
  `reference_id` int(11) DEFAULT NULL,
  `status` varchar(50) DEFAULT 'unread',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`notification_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert sample notifications
INSERT IGNORE INTO `notifications` (`notification_id`, `user_id`, `message`, `type`, `reference_id`, `status`, `is_read`) VALUES
(1, 1, 'New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on August 15, 2024', 'booking', 100, 'read', 1),
(2, 1, 'New booking received from Maria Santos for Sicogon Island tour on August 20, 2024', 'booking', 101, 'unread', 0),
(3, 1, 'New booking received from Robert Garcia Sr. for Island Hopping tour on August 25, 2024', 'booking', 102, 'unread', 0),
(4, 1, 'Booking BOOK-2024-100 has been confirmed and payment received', 'payment', 100, 'read', 1),
(5, 1, 'Booking BOOK-2024-102 has been accepted and is ready for tour', 'status', 102, 'unread', 0);

-- --------------------------------------------------------

--
-- Table structure for table `tourist`
--

CREATE TABLE IF NOT EXISTS `tourist` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `full_name` varchar(100) NOT NULL,
  `address` varchar(255) DEFAULT NULL,
  `mobile_number` varchar(20) DEFAULT NULL,
  `email_address` varchar(100) DEFAULT NULL,
  `sex` enum('Male','Female','Other') DEFAULT NULL,
  `birthdate` date DEFAULT NULL,
  `date_of_tour` date DEFAULT NULL,
  `number_of_pax` int(11) DEFAULT 1,
  `tour_destination` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- AUTO_INCREMENT settings
--

ALTER TABLE `admins` MODIFY `admin_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;
ALTER TABLE `boats` MODIFY `boat_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;
ALTER TABLE `bookings` MODIFY `booking_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=100;
ALTER TABLE `booking_passengers` MODIFY `passenger_id` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `destinations` MODIFY `destination_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;
ALTER TABLE `notifications` MODIFY `notification_id` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tourist` MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT;

COMMIT;
