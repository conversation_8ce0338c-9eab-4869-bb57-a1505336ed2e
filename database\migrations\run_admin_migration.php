<?php
// Migration script to add first_name and last_name to admins table
// Run this once to fix the sidebar.php error

require_once __DIR__ . '/../../admin/includes/config.php';

try {
    echo "Starting admin table migration...\n";
    
    // Check if columns already exist
    $check_query = "SHOW COLUMNS FROM admins LIKE 'first_name'";
    $result = $con->query($check_query);
    
    if ($result->num_rows > 0) {
        echo "Columns already exist. Skipping migration.\n";
        exit;
    }
    
    // Add first_name and last_name columns
    echo "Adding first_name and last_name columns...\n";
    $alter_query = "ALTER TABLE `admins` 
                    ADD COLUMN `first_name` varchar(50) NOT NULL COMMENT 'Admin first name' AFTER `password`,
                    ADD COLUMN `last_name` varchar(50) NOT NULL COMMENT 'Admin last name' AFTER `first_name`";
    
    if ($con->query($alter_query) === TRUE) {
        echo "Columns added successfully.\n";
    } else {
        throw new Exception("Error adding columns: " . $con->error);
    }
    
    // Update existing admin record
    echo "Updating existing admin records...\n";
    $update_query = "UPDATE `admins` 
                     SET `first_name` = 'System', `last_name` = 'Administrator' 
                     WHERE `admin_id` = 1";
    
    if ($con->query($update_query) === TRUE) {
        echo "Admin record updated successfully.\n";
    } else {
        throw new Exception("Error updating admin record: " . $con->error);
    }
    
    // Update any other existing admin records
    $update_others_query = "UPDATE `admins` 
                           SET `first_name` = SUBSTRING_INDEX(`full_name`, ' ', 1),
                               `last_name` = TRIM(SUBSTRING(`full_name`, LOCATE(' ', `full_name`) + 1))
                           WHERE (`first_name` = '' OR `last_name` = '') AND `admin_id` != 1";
    
    if ($con->query($update_others_query) === TRUE) {
        echo "Other admin records updated successfully.\n";
    } else {
        echo "Warning: Could not update other admin records: " . $con->error . "\n";
    }
    
    echo "Migration completed successfully!\n";
    echo "You can now access the admin dashboard without errors.\n";
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}

$con->close();
?>
