-- Migration: Add first_name and last_name columns to admins table
-- Date: 2025-07-31
-- Purpose: Fix sidebar.php error by adding required name columns

-- Add first_name and last_name columns to admins table
ALTER TABLE `admins` 
ADD COLUMN `first_name` varchar(50) NOT NULL COMMENT 'Admin first name' AFTER `password`,
ADD COLUMN `last_name` varchar(50) NOT NULL COMMENT 'Admin last name' AFTER `first_name`;

-- Update existing admin record with proper names
UPDATE `admins` 
SET `first_name` = 'System', `last_name` = 'Administrator' 
WHERE `admin_id` = 1;

-- Update any other existing admin records (if any)
UPDATE `admins` 
SET `first_name` = SUBSTRING_INDEX(`full_name`, ' ', 1),
    `last_name` = TRIM(SUBSTRING(`full_name`, LOCATE(' ', `full_name`) + 1))
WHERE `first_name` = '' OR `last_name` = '';
