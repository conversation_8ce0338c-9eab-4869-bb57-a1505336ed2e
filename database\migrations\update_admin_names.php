<?php
// Update admin names
require_once __DIR__ . '/../../admin/includes/config.php';

try {
    echo "Updating admin names...\n";
    
    // Update the main admin record
    $update_query = "UPDATE `admins` 
                     SET `first_name` = 'System', `last_name` = 'Administrator' 
                     WHERE `admin_id` = 1";
    
    if ($con->query($update_query) === TRUE) {
        echo "Admin record updated successfully.\n";
    } else {
        throw new Exception("Error updating admin record: " . $con->error);
    }
    
    // Check the result
    $check_query = "SELECT admin_id, username, first_name, last_name, full_name FROM admins WHERE admin_id = 1";
    $result = $con->query($check_query);
    $row = $result->fetch_assoc();
    
    echo "Updated admin record:\n";
    echo "ID: {$row['admin_id']}, Username: {$row['username']}, First: '{$row['first_name']}', Last: '{$row['last_name']}', Full: '{$row['full_name']}'\n";
    
    echo "\nUpdate completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$con->close();
?>
