-- =====================================================
-- CARLES TOURISM ONLINE BOOKING SYSTEM DATABASE
-- =====================================================
-- Updated: July 31, 2025
-- Description: Clean and optimized database structure for boat booking system
-- Compatible with: MySQL 5.7+ / MariaDB 10.4+
-- All fields match booking.html form exactly
-- =====================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";
SET FOREIGN_KEY_CHECKS = 0;
SET NAMES utf8mb4;

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS `booking_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `booking_system`;

-- =====================================================
-- CORE TABLES
-- =====================================================

-- Table: destinations
-- Purpose: Store available tour destinations from booking form
CREATE TABLE `destinations` (
  `destination_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`destination_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert destinations matching booking.html form options
INSERT INTO `destinations` (`destination_id`, `name`, `description`, `status`) VALUES
(1, 'Gigantes Island', 'Popular island destination with beautiful beaches', 'active'),
(2, 'Sicogon Island', 'Pristine island with white sand beaches', 'active'),
(3, 'Tumaquin Island', 'Scenic island perfect for day trips', 'active'),
(4, 'Cabugao Gamay Island', 'Small island with crystal clear waters', 'active'),
(5, 'Calagnaan Island', 'Peaceful island destination', 'active');

-- Table: customers
-- Purpose: Store customer information for registered users
CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL UNIQUE,
  `contact_number` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table: boats
-- Purpose: Store boat information for assignments
CREATE TABLE `boats` (
  `boat_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` varchar(50) DEFAULT NULL,
  `capacity` int(11) NOT NULL DEFAULT 25,
  `price_per_day` decimal(10,2) DEFAULT 0.00,
  `availability_status` enum('available','maintenance','unavailable') DEFAULT 'available',
  `description` text DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`boat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert default boats
INSERT INTO `boats` (`boat_id`, `name`, `type`, `capacity`, `price_per_day`, `availability_status`) VALUES
(1, 'Tourism Office Boat 1', 'Standard', 25, 0.00, 'available'),
(2, 'Tourism Office Boat 2', 'Standard', 25, 0.00, 'available'),
(3, 'Tourism Office Boat 3', 'Standard', 25, 0.00, 'available');

-- Table: admins
-- Purpose: Store admin user accounts for system management
CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `role` enum('super_admin','admin','staff') DEFAULT 'admin',
  `status` enum('active','inactive') DEFAULT 'active',
  `last_login` datetime DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert default admin account (password: 'password')
INSERT INTO `admins` (`admin_id`, `username`, `password`, `full_name`, `email`, `role`, `status`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', '<EMAIL>', 'super_admin', 'active');

-- =====================================================
-- MAIN BOOKINGS TABLE
-- =====================================================
-- Purpose: Store all booking information from booking.html form
-- This table contains ALL fields from the booking form

CREATE TABLE `bookings` (
  `booking_id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) DEFAULT NULL,
  
  -- Personal Information (from booking form step 1)
  `first_name` varchar(255) NOT NULL COMMENT 'firstName field from form',
  `last_name` varchar(255) NOT NULL COMMENT 'lastName field from form',
  `suffix` varchar(10) DEFAULT NULL COMMENT 'suffix field from form',
  `age` int(11) NOT NULL COMMENT 'age field from form',
  `sex` varchar(10) NOT NULL COMMENT 'sex field from form',
  `contact_number` varchar(20) NOT NULL COMMENT 'contactNumber field from form',
  `email` varchar(100) NOT NULL COMMENT 'emailAddress field from form',
  `address` varchar(255) NOT NULL COMMENT 'completeAddress field from form',
  
  -- Emergency Contact Information
  `emergency_name` varchar(255) NOT NULL COMMENT 'emergencyName field from form',
  `emergency_number` varchar(20) NOT NULL COMMENT 'emergencyNumber field from form',
  
  -- Tour Information (from booking form step 2)
  `tour_destination` varchar(255) NOT NULL COMMENT 'locationTourDestination field from form',
  `drop_off_location` varchar(255) NOT NULL COMMENT 'dropOffLocation field from form',
  `destination_id` int(11) NOT NULL DEFAULT 1 COMMENT 'Links to destinations table',
  
  -- Passenger Information
  `no_of_pax` int(11) NOT NULL COMMENT 'numberOfPax field from form',
  `total_passengers` int(11) NOT NULL COMMENT 'totalPassengers field from form',
  `regular_pax` int(11) NOT NULL DEFAULT 0 COMMENT 'regularPax field from form',
  `discounted_pax` int(11) NOT NULL DEFAULT 0 COMMENT 'discountedPax field from form',
  `children_pax` int(11) NOT NULL DEFAULT 0 COMMENT 'childrenPax field from form',
  `infants_pax` int(11) NOT NULL DEFAULT 0 COMMENT 'infantsPax field from form',
  
  -- Date Information
  `start_date` datetime NOT NULL COMMENT 'startDate field from form',
  `end_date` datetime NOT NULL COMMENT 'endDate field from form',
  
  -- Payment Information (from booking form step 3)
  `payment_method` varchar(50) NOT NULL COMMENT 'paymentMethod field from form (GCash/Manual Payment)',
  `payment_proof` varchar(255) DEFAULT NULL COMMENT 'gcashProofUpload field from form',
  `environmental_fee` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'totalEnvironmentalFee field from form',
  `total` decimal(10,2) NOT NULL COMMENT 'total field from form',
  
  -- System Fields
  `boat_id` int(11) NOT NULL DEFAULT 1 COMMENT 'selectedBoat field from form',
  `booking_code` varchar(50) NOT NULL UNIQUE COMMENT 'Auto-generated booking reference',
  `booking_status` enum('pending','confirmed','cancelled','accepted','rejected','verification_pending') NOT NULL DEFAULT 'pending',
  `booking_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_today_booking` tinyint(1) DEFAULT 1 COMMENT 'currentDate field from form',
  `passenger_details` text DEFAULT NULL COMMENT 'Additional passenger information',
  
  PRIMARY KEY (`booking_id`),
  KEY `customer_id` (`customer_id`),
  KEY `destination_id` (`destination_id`),
  KEY `boat_id` (`boat_id`),
  KEY `booking_status` (`booking_status`),
  KEY `start_date` (`start_date`),
  CONSTRAINT `fk_bookings_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_bookings_destination` FOREIGN KEY (`destination_id`) REFERENCES `destinations` (`destination_id`),
  CONSTRAINT `fk_bookings_boat` FOREIGN KEY (`boat_id`) REFERENCES `boats` (`boat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Main bookings table - matches all fields from booking.html form';

-- Table: booking_passengers
-- Purpose: Store individual passenger details for each booking
CREATE TABLE `booking_passengers` (
  `passenger_id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_id` int(11) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `age` int(11) NOT NULL,
  `sex` varchar(10) NOT NULL,
  `passenger_type` enum('regular','discounted','children','infants') NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`passenger_id`),
  KEY `booking_id` (`booking_id`),
  CONSTRAINT `fk_passengers_booking` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`booking_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- SAMPLE DATA
-- =====================================================

-- Sample booking data that matches the booking form structure
INSERT INTO `bookings` (
  `booking_id`, `customer_id`, `first_name`, `last_name`, `suffix`, `age`, `sex`, 
  `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`,
  `tour_destination`, `drop_off_location`, `destination_id`, `no_of_pax`, 
  `total_passengers`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`,
  `start_date`, `end_date`, `payment_method`, `payment_proof`, `environmental_fee`, 
  `total`, `boat_id`, `booking_code`, `booking_status`, `booking_time`, `created_at`
) VALUES
(1, NULL, 'Juan', 'Dela Cruz', 'Jr.', 25, 'Male', '09123456789', '<EMAIL>', 
 'Carles, Iloilo', 'Maria Dela Cruz', '09987654321', 'Gigantes Island', 'Carles Port', 
 1, 4, 4, 2, 1, 1, 0, '2025-08-01 08:00:00', '2025-08-01 17:00:00', 'GCash', 
 'gcash_proof_001.jpg', 210.00, 210.00, 1, 'BOAT1-20250801-001', 'confirmed', 
 '2025-07-31 10:00:00', '2025-07-31 10:00:00'),

(2, NULL, 'Maria', 'Santos', '', 30, 'Female', '09111222333', '<EMAIL>', 
 'Estancia, Iloilo', 'Pedro Santos', '09444555666', 'Sicogon Island', 'Estancia Port', 
 2, 2, 2, 2, 0, 0, 0, '2025-08-02 09:00:00', '2025-08-02 16:00:00', 'Manual Payment', 
 NULL, 150.00, 150.00, 2, 'BOAT2-20250802-002', 'pending', 
 '2025-07-31 11:30:00', '2025-07-31 11:30:00');

-- =====================================================
-- AUTO INCREMENT SETTINGS
-- =====================================================

-- Set AUTO_INCREMENT values for all tables
ALTER TABLE `destinations` AUTO_INCREMENT = 6;
ALTER TABLE `customers` AUTO_INCREMENT = 1;
ALTER TABLE `boats` AUTO_INCREMENT = 4;
ALTER TABLE `bookings` AUTO_INCREMENT = 3;
ALTER TABLE `admins` AUTO_INCREMENT = 2;
ALTER TABLE `booking_passengers` AUTO_INCREMENT = 1;

-- =====================================================
-- ENABLE FOREIGN KEY CHECKS AND COMMIT
-- =====================================================

SET FOREIGN_KEY_CHECKS = 1;
COMMIT;

-- =====================================================
-- END OF CARLES TOURISM BOOKING SYSTEM DATABASE
-- =====================================================
