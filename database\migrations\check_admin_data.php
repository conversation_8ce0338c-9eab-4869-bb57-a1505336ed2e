<?php
// Check admin table data
require_once __DIR__ . '/../../admin/includes/config.php';

try {
    echo "Checking admin table structure and data...\n\n";
    
    // Check table structure
    $structure_query = "DESCRIBE admins";
    $result = $con->query($structure_query);
    
    echo "Admin table structure:\n";
    while ($row = $result->fetch_assoc()) {
        echo "- {$row['Field']}: {$row['Type']}\n";
    }
    
    echo "\nAdmin records:\n";
    $data_query = "SELECT admin_id, username, first_name, last_name, full_name, email FROM admins";
    $result = $con->query($data_query);
    
    while ($row = $result->fetch_assoc()) {
        echo "ID: {$row['admin_id']}, Username: {$row['username']}, First: '{$row['first_name']}', Last: '{$row['last_name']}', Full: '{$row['full_name']}'\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$con->close();
?>
