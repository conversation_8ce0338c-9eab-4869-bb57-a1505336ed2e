2025-07-31 06:12:05 - Starting notification fetch
2025-07-31 12:12:05 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:12:05 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:12:05 - Found notification: ID=1, Message=New booking received from <PERSON>. for Gigantes Island tour on 2025-07-31
2025-07-31 12:12:05 - Found notification: ID=2, Message=New booking received from Maria Santos for Sicogon Island tour on 2025-08-01
2025-07-31 12:12:05 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:12:05 - Found notification: ID=4, Message=New booking received from <PERSON>. for Island Hopping tour on 2025-08-07
2025-07-31 12:12:05 - Found 4 notifications
2025-07-31 06:12:16 - Starting notification fetch
2025-07-31 12:12:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:12:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:12:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:12:16 - Found notification: ID=2, Message=New booking received from Maria Santos for Sicogon Island tour on 2025-08-01
2025-07-31 12:12:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:12:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:12:16 - Found 4 notifications
2025-07-31 06:12:35 - Starting notification fetch
2025-07-31 12:12:35 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:12:35 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:12:35 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:12:35 - Found notification: ID=2, Message=New booking received from Maria Santos for Sicogon Island tour on 2025-08-01
2025-07-31 12:12:35 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:12:35 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:12:35 - Found 4 notifications
2025-07-31 06:13:05 - Starting notification fetch
2025-07-31 12:13:05 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:13:05 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:13:05 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:13:05 - Found notification: ID=2, Message=New booking received from Maria Santos for Sicogon Island tour on 2025-08-01
2025-07-31 12:13:05 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:13:05 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:13:05 - Found 4 notifications
2025-07-31 06:13:16 - Starting notification fetch
2025-07-31 12:13:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:13:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:13:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:13:16 - Found notification: ID=2, Message=New booking received from Maria Santos for Sicogon Island tour on 2025-08-01
2025-07-31 12:13:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:13:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:13:16 - Found 4 notifications
2025-07-31 06:13:35 - Starting notification fetch
2025-07-31 12:13:35 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:13:35 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:13:35 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:13:35 - Found notification: ID=2, Message=New booking received from Maria Santos for Sicogon Island tour on 2025-08-01
2025-07-31 12:13:35 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:13:35 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:13:35 - Found 4 notifications
2025-07-31 06:14:05 - Starting notification fetch
2025-07-31 12:14:05 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:14:05 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:14:05 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:14:05 - Found notification: ID=2, Message=New booking received from Maria Santos for Sicogon Island tour on 2025-08-01
2025-07-31 12:14:05 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:14:05 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:14:05 - Found 4 notifications
2025-07-31 06:14:16 - Starting notification fetch
2025-07-31 12:14:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:14:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:14:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:14:16 - Found notification: ID=2, Message=New booking received from Maria Santos for Sicogon Island tour on 2025-08-01
2025-07-31 12:14:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:14:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:14:16 - Found 4 notifications
2025-07-31 06:14:35 - Starting notification fetch
2025-07-31 12:14:35 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:14:35 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:14:35 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:14:35 - Found notification: ID=2, Message=New booking received from Maria Santos for Sicogon Island tour on 2025-08-01
2025-07-31 12:14:35 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:14:35 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:14:35 - Found 4 notifications
2025-07-31 06:15:05 - Starting notification fetch
2025-07-31 12:15:05 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:15:05 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:15:05 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:15:05 - Found notification: ID=2, Message=New booking received from Maria Santos for Sicogon Island tour on 2025-08-01
2025-07-31 12:15:05 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:15:05 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:15:05 - Found 4 notifications
2025-07-31 06:15:16 - Starting notification fetch
2025-07-31 12:15:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:15:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:15:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:15:16 - Found notification: ID=2, Message=New booking received from Maria Santos for Sicogon Island tour on 2025-08-01
2025-07-31 12:15:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:15:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:15:16 - Found 4 notifications
2025-07-31 06:15:35 - Starting notification fetch
2025-07-31 12:15:35 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:15:35 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:15:35 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:15:35 - Found notification: ID=2, Message=New booking received from Maria Santos for Sicogon Island tour on 2025-08-01
2025-07-31 12:15:35 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:15:35 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:15:35 - Found 4 notifications
2025-07-31 12:15:38 - Mark read request received for ID: 2
2025-07-31 12:15:38 - Notification marked as read: ID=2
2025-07-31 06:16:16 - Starting notification fetch
2025-07-31 12:16:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:16:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:16:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:16:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:16:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:16:16 - Found 3 notifications
2025-07-31 06:17:12 - Starting notification fetch
2025-07-31 12:17:12 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:17:12 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:17:12 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:17:12 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:17:12 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:17:12 - Found 3 notifications
2025-07-31 06:17:16 - Starting notification fetch
2025-07-31 12:17:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:17:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:17:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:17:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:17:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:17:16 - Found 3 notifications
2025-07-31 06:18:02 - Starting notification fetch
2025-07-31 12:18:02 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:18:02 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:18:02 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:18:02 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:18:02 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:18:02 - Found 3 notifications
2025-07-31 06:18:16 - Starting notification fetch
2025-07-31 12:18:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:18:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:18:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:18:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:18:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:18:16 - Found 3 notifications
2025-07-31 06:18:32 - Starting notification fetch
2025-07-31 12:18:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:18:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:18:32 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:18:32 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:18:32 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:18:32 - Found 3 notifications
2025-07-31 06:19:02 - Starting notification fetch
2025-07-31 12:19:02 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:19:02 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:19:02 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:19:02 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:19:02 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:19:02 - Found 3 notifications
2025-07-31 06:19:16 - Starting notification fetch
2025-07-31 12:19:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:19:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:19:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:19:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:19:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:19:16 - Found 3 notifications
2025-07-31 06:19:32 - Starting notification fetch
2025-07-31 12:19:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:19:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:19:32 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:19:32 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:19:32 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:19:32 - Found 3 notifications
2025-07-31 06:20:02 - Starting notification fetch
2025-07-31 12:20:02 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:20:02 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:20:02 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:20:02 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:20:02 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:20:02 - Found 3 notifications
2025-07-31 06:20:16 - Starting notification fetch
2025-07-31 12:20:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:20:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:20:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:20:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:20:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:20:16 - Found 3 notifications
2025-07-31 06:20:32 - Starting notification fetch
2025-07-31 12:20:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:20:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:20:32 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:20:32 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:20:32 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:20:32 - Found 3 notifications
2025-07-31 06:21:02 - Starting notification fetch
2025-07-31 12:21:02 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:21:02 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:21:02 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:21:02 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:21:02 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:21:02 - Found 3 notifications
2025-07-31 06:21:16 - Starting notification fetch
2025-07-31 12:21:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:21:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:21:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:21:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:21:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:21:16 - Found 3 notifications
2025-07-31 06:21:32 - Starting notification fetch
2025-07-31 12:21:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:21:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:21:32 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:21:32 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:21:32 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:21:32 - Found 3 notifications
2025-07-31 06:22:02 - Starting notification fetch
2025-07-31 12:22:02 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:22:02 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:22:02 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:22:02 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:22:02 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:22:02 - Found 3 notifications
2025-07-31 06:22:16 - Starting notification fetch
2025-07-31 12:22:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:22:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:22:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:22:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:22:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:22:16 - Found 3 notifications
2025-07-31 06:22:33 - Starting notification fetch
2025-07-31 12:22:33 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:22:33 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:22:33 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:22:33 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:22:33 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:22:33 - Found 3 notifications
2025-07-31 06:23:03 - Starting notification fetch
2025-07-31 12:23:03 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:23:03 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:23:03 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:23:03 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:23:03 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:23:03 - Found 3 notifications
2025-07-31 06:23:16 - Starting notification fetch
2025-07-31 12:23:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:23:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:23:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:23:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:23:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:23:16 - Found 3 notifications
2025-07-31 06:23:33 - Starting notification fetch
2025-07-31 12:23:33 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:23:33 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:23:33 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:23:33 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:23:33 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:23:33 - Found 3 notifications
2025-07-31 06:24:16 - Starting notification fetch
2025-07-31 12:24:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:24:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:24:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:24:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:24:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:24:16 - Found 3 notifications
2025-07-31 06:24:17 - Starting notification fetch
2025-07-31 12:24:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:24:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:24:17 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:24:17 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:24:17 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:24:17 - Found 3 notifications
2025-07-31 06:24:48 - Starting notification fetch
2025-07-31 12:24:48 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:24:48 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:24:48 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:24:48 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:24:48 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:24:48 - Found 3 notifications
2025-07-31 06:25:16 - Starting notification fetch
2025-07-31 12:25:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:25:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:25:16 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:25:16 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:25:16 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:25:16 - Found 3 notifications
2025-07-31 06:25:18 - Starting notification fetch
2025-07-31 12:25:18 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:25:18 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:25:18 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:25:18 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:25:18 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:25:18 - Found 3 notifications
2025-07-31 06:25:40 - Starting notification fetch
2025-07-31 12:25:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:25:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:25:40 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:25:40 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:25:40 - Found notification: ID=4, Message=New booking received from Robert Garcia Sr. for Island Hopping tour on 2025-08-07
2025-07-31 12:25:40 - Found 3 notifications
2025-07-31 12:25:45 - Mark read request received for ID: 4
2025-07-31 12:25:45 - Notification marked as read: ID=4
2025-07-31 06:25:48 - Starting notification fetch
2025-07-31 06:25:48 - Starting notification fetch
2025-07-31 12:25:48 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:25:48 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:25:48 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:25:48 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:25:48 - Found 2 notifications
2025-07-31 12:25:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:25:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:25:49 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:25:49 - Found notification: ID=3, Message=Booking BOOK-2025-101 has been confirmed and payment received
2025-07-31 12:25:49 - Found 2 notifications
2025-07-31 12:25:49 - Mark read request received for ID: 3
2025-07-31 12:25:49 - Notification marked as read: ID=3
2025-07-31 06:25:51 - Starting notification fetch
2025-07-31 06:25:51 - Starting notification fetch
2025-07-31 12:25:51 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:25:51 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:25:51 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:25:51 - Found 1 notifications
2025-07-31 12:25:51 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:25:51 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:25:51 - Found notification: ID=1, Message=New booking received from Juan Dela Cruz Jr. for Gigantes Island tour on 2025-07-31
2025-07-31 12:25:51 - Found 1 notifications
2025-07-31 12:25:52 - Mark read request received for ID: 1
2025-07-31 12:25:52 - Notification marked as read: ID=1
2025-07-31 06:26:16 - Starting notification fetch
2025-07-31 12:26:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:26:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:26:16 - Found 0 notifications
2025-07-31 06:27:16 - Starting notification fetch
2025-07-31 12:27:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:27:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:27:16 - Found 0 notifications
2025-07-31 06:28:16 - Starting notification fetch
2025-07-31 12:28:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:28:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:28:16 - Found 0 notifications
2025-07-31 06:29:16 - Starting notification fetch
2025-07-31 12:29:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:29:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:29:16 - Found 0 notifications
2025-07-31 06:30:16 - Starting notification fetch
2025-07-31 12:30:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:30:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:30:16 - Found 0 notifications
2025-07-31 06:30:51 - Starting notification fetch
2025-07-31 12:30:51 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:30:51 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:30:51 - Found 0 notifications
2025-07-31 06:31:16 - Starting notification fetch
2025-07-31 12:31:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:31:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:31:16 - Found 0 notifications
2025-07-31 06:31:21 - Starting notification fetch
2025-07-31 12:31:21 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:31:21 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:31:21 - Found 0 notifications
2025-07-31 06:31:51 - Starting notification fetch
2025-07-31 12:31:51 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:31:51 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:31:51 - Found 0 notifications
2025-07-31 06:32:16 - Starting notification fetch
2025-07-31 12:32:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:32:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:32:16 - Found 0 notifications
2025-07-31 06:32:21 - Starting notification fetch
2025-07-31 12:32:21 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:32:21 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:32:21 - Found 0 notifications
2025-07-31 06:32:51 - Starting notification fetch
2025-07-31 12:32:51 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:32:51 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:32:51 - Found 0 notifications
2025-07-31 06:33:05 - Starting notification fetch
2025-07-31 12:33:05 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:33:05 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:33:05 - Found 0 notifications
2025-07-31 06:33:21 - Starting notification fetch
2025-07-31 12:33:21 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:33:21 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:33:21 - Found 0 notifications
2025-07-31 06:33:49 - Starting notification fetch
2025-07-31 12:33:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:33:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:33:49 - Found 0 notifications
2025-07-31 06:33:51 - Starting notification fetch
2025-07-31 12:33:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:33:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:33:52 - Found 0 notifications
2025-07-31 06:34:19 - Starting notification fetch
2025-07-31 12:34:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:34:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:34:19 - Found 0 notifications
2025-07-31 06:34:22 - Starting notification fetch
2025-07-31 12:34:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:34:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:34:22 - Found 0 notifications
2025-07-31 06:34:49 - Starting notification fetch
2025-07-31 12:34:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:34:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:34:49 - Found 0 notifications
2025-07-31 06:34:52 - Starting notification fetch
2025-07-31 12:34:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:34:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:34:52 - Found 0 notifications
2025-07-31 06:35:19 - Starting notification fetch
2025-07-31 12:35:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:35:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:35:19 - Found 0 notifications
2025-07-31 06:35:22 - Starting notification fetch
2025-07-31 12:35:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:35:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:35:22 - Found 0 notifications
2025-07-31 06:35:49 - Starting notification fetch
2025-07-31 12:35:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:35:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:35:49 - Found 0 notifications
2025-07-31 06:35:52 - Starting notification fetch
2025-07-31 12:35:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:35:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:35:52 - Found 0 notifications
2025-07-31 06:36:18 - Starting notification fetch
2025-07-31 12:36:18 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:36:18 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:36:18 - Found 0 notifications
2025-07-31 06:36:22 - Starting notification fetch
2025-07-31 12:36:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:36:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:36:22 - Found 0 notifications
2025-07-31 06:36:52 - Starting notification fetch
2025-07-31 12:36:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:36:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:36:52 - Found 0 notifications
2025-07-31 06:36:56 - Starting notification fetch
2025-07-31 12:36:57 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:36:57 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:36:57 - Found 0 notifications
2025-07-31 06:37:22 - Starting notification fetch
2025-07-31 12:37:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:37:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:37:22 - Found 0 notifications
2025-07-31 06:37:27 - Starting notification fetch
2025-07-31 12:37:27 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:37:27 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:37:27 - Found 0 notifications
2025-07-31 06:37:52 - Starting notification fetch
2025-07-31 12:37:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:37:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:37:52 - Found 0 notifications
2025-07-31 06:37:57 - Starting notification fetch
2025-07-31 12:37:57 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:37:57 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:37:57 - Found 0 notifications
2025-07-31 06:38:22 - Starting notification fetch
2025-07-31 12:38:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:38:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:38:22 - Found 0 notifications
2025-07-31 06:38:27 - Starting notification fetch
2025-07-31 12:38:27 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:38:27 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:38:27 - Found 0 notifications
2025-07-31 06:38:52 - Starting notification fetch
2025-07-31 12:38:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:38:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:38:52 - Found 0 notifications
2025-07-31 06:38:57 - Starting notification fetch
2025-07-31 12:38:57 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:38:57 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:38:57 - Found 0 notifications
2025-07-31 06:39:22 - Starting notification fetch
2025-07-31 12:39:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:39:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:39:22 - Found 0 notifications
2025-07-31 06:39:29 - Starting notification fetch
2025-07-31 12:39:29 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:39:29 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:39:29 - Found 0 notifications
2025-07-31 06:39:52 - Starting notification fetch
2025-07-31 12:39:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:39:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:39:52 - Found 0 notifications
2025-07-31 06:39:57 - Starting notification fetch
2025-07-31 12:39:57 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:39:57 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:39:57 - Found 0 notifications
2025-07-31 06:40:22 - Starting notification fetch
2025-07-31 12:40:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:40:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:40:22 - Found 0 notifications
2025-07-31 06:40:27 - Starting notification fetch
2025-07-31 12:40:27 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:40:27 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:40:27 - Found 0 notifications
2025-07-31 06:40:46 - Starting notification fetch
2025-07-31 12:40:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:40:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:40:47 - Found 0 notifications
2025-07-31 06:40:52 - Starting notification fetch
2025-07-31 12:40:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:40:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:40:52 - Found 0 notifications
2025-07-31 06:41:12 - Starting notification fetch
2025-07-31 12:41:12 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:41:12 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:41:12 - Found 0 notifications
2025-07-31 06:41:16 - Starting notification fetch
2025-07-31 12:41:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:41:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:41:16 - Found 0 notifications
2025-07-31 06:41:22 - Starting notification fetch
2025-07-31 12:41:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:41:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:41:22 - Found 0 notifications
2025-07-31 06:41:41 - Starting notification fetch
2025-07-31 12:41:41 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:41:41 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:41:41 - Found 0 notifications
2025-07-31 06:41:50 - Starting notification fetch
2025-07-31 12:41:50 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:41:50 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:41:50 - Found 0 notifications
2025-07-31 06:41:52 - Starting notification fetch
2025-07-31 12:41:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:41:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:41:52 - Found 0 notifications
2025-07-31 06:42:11 - Starting notification fetch
2025-07-31 12:42:11 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:42:11 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:42:11 - Found 0 notifications
2025-07-31 06:42:22 - Starting notification fetch
2025-07-31 12:42:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:42:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:42:22 - Found 0 notifications
2025-07-31 06:42:41 - Starting notification fetch
2025-07-31 12:42:41 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:42:41 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:42:41 - Found 0 notifications
2025-07-31 06:42:52 - Starting notification fetch
2025-07-31 12:42:53 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:42:53 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:42:53 - Found 0 notifications
2025-07-31 06:43:23 - Starting notification fetch
2025-07-31 12:43:23 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:43:23 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:43:23 - Found 0 notifications
2025-07-31 06:43:53 - Starting notification fetch
2025-07-31 12:43:53 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:43:53 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:43:53 - Found 0 notifications
2025-07-31 06:44:23 - Starting notification fetch
2025-07-31 12:44:23 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:44:23 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:44:23 - Found 0 notifications
2025-07-31 06:44:53 - Starting notification fetch
2025-07-31 12:44:53 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:44:53 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:44:53 - Found 0 notifications
2025-07-31 06:45:23 - Starting notification fetch
2025-07-31 12:45:23 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:45:23 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:45:23 - Found 0 notifications
2025-07-31 06:45:53 - Starting notification fetch
2025-07-31 12:45:53 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:45:53 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:45:53 - Found 0 notifications
2025-07-31 06:46:23 - Starting notification fetch
2025-07-31 12:46:23 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:46:23 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:46:23 - Found 0 notifications
2025-07-31 06:46:53 - Starting notification fetch
2025-07-31 12:46:53 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:46:53 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:46:53 - Found 0 notifications
2025-07-31 06:47:23 - Starting notification fetch
2025-07-31 12:47:23 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:47:23 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:47:23 - Found 0 notifications
2025-07-31 06:47:53 - Starting notification fetch
2025-07-31 12:47:53 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:47:53 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:47:53 - Found 0 notifications
2025-07-31 06:48:23 - Starting notification fetch
2025-07-31 12:48:23 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:48:23 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:48:23 - Found 0 notifications
2025-07-31 06:48:53 - Starting notification fetch
2025-07-31 12:48:53 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:48:53 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:48:53 - Found 0 notifications
2025-07-31 06:49:23 - Starting notification fetch
2025-07-31 12:49:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:49:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:49:24 - Found 0 notifications
2025-07-31 06:49:53 - Starting notification fetch
2025-07-31 12:49:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:49:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:49:54 - Found 0 notifications
2025-07-31 06:50:24 - Starting notification fetch
2025-07-31 12:50:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:50:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:50:24 - Found 0 notifications
2025-07-31 06:50:54 - Starting notification fetch
2025-07-31 12:50:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:50:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:50:54 - Found 0 notifications
2025-07-31 06:51:24 - Starting notification fetch
2025-07-31 12:51:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:51:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:51:24 - Found 0 notifications
2025-07-31 06:51:54 - Starting notification fetch
2025-07-31 12:51:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:51:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:51:54 - Found 0 notifications
2025-07-31 06:52:24 - Starting notification fetch
2025-07-31 12:52:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:52:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:52:24 - Found 0 notifications
2025-07-31 06:53:24 - Starting notification fetch
2025-07-31 12:53:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:53:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:53:24 - Found 0 notifications
2025-07-31 06:53:54 - Starting notification fetch
2025-07-31 12:53:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 12:53:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 12:53:54 - Found 0 notifications
2025-07-31 07:42:48 - Starting notification fetch
2025-07-31 13:42:48 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:42:48 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:42:48 - Found 0 notifications
2025-07-31 07:43:18 - Starting notification fetch
2025-07-31 13:43:18 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:43:18 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:43:18 - Found 0 notifications
2025-07-31 07:43:48 - Starting notification fetch
2025-07-31 13:43:48 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:43:48 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:43:48 - Found 0 notifications
2025-07-31 07:44:18 - Starting notification fetch
2025-07-31 13:44:18 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:44:18 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:44:18 - Found 0 notifications
2025-07-31 07:44:30 - Starting notification fetch
2025-07-31 13:44:31 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:44:31 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:44:31 - Found 0 notifications
2025-07-31 07:44:48 - Starting notification fetch
2025-07-31 13:44:48 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:44:48 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:44:48 - Found 0 notifications
2025-07-31 07:45:00 - Starting notification fetch
2025-07-31 13:45:01 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:45:01 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:45:01 - Found 0 notifications
2025-07-31 07:45:19 - Starting notification fetch
2025-07-31 13:45:20 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:45:20 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:45:20 - Found 0 notifications
2025-07-31 07:45:30 - Starting notification fetch
2025-07-31 13:45:31 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:45:31 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:45:31 - Found 0 notifications
2025-07-31 07:45:49 - Starting notification fetch
2025-07-31 13:45:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:45:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:45:49 - Found 0 notifications
2025-07-31 07:45:51 - Starting notification fetch
2025-07-31 13:45:51 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:45:51 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:45:51 - Found 0 notifications
2025-07-31 07:46:01 - Starting notification fetch
2025-07-31 13:46:01 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:46:01 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:46:01 - Found 0 notifications
2025-07-31 07:46:19 - Starting notification fetch
2025-07-31 13:46:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:46:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:46:19 - Found 0 notifications
2025-07-31 07:46:21 - Starting notification fetch
2025-07-31 13:46:21 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:46:21 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:46:21 - Found 0 notifications
2025-07-31 07:46:32 - Starting notification fetch
2025-07-31 13:46:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:46:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:46:32 - Found 0 notifications
2025-07-31 07:46:50 - Starting notification fetch
2025-07-31 13:46:50 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:46:50 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:46:50 - Found 0 notifications
2025-07-31 07:46:52 - Starting notification fetch
2025-07-31 13:46:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:46:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:46:52 - Found 0 notifications
2025-07-31 07:47:02 - Starting notification fetch
2025-07-31 13:47:02 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:47:02 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:47:02 - Found 0 notifications
2025-07-31 07:47:20 - Starting notification fetch
2025-07-31 13:47:20 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:47:20 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:47:20 - Found 0 notifications
2025-07-31 07:47:22 - Starting notification fetch
2025-07-31 13:47:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:47:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:47:22 - Found 0 notifications
2025-07-31 07:47:32 - Starting notification fetch
2025-07-31 13:47:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:47:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:47:32 - Found 0 notifications
2025-07-31 07:47:50 - Starting notification fetch
2025-07-31 13:47:50 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:47:50 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:47:50 - Found 0 notifications
2025-07-31 07:47:52 - Starting notification fetch
2025-07-31 13:47:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:47:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:47:52 - Found 0 notifications
2025-07-31 07:48:02 - Starting notification fetch
2025-07-31 13:48:02 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:48:02 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:48:02 - Found 0 notifications
2025-07-31 07:48:20 - Starting notification fetch
2025-07-31 13:48:20 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:48:20 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:48:20 - Found 0 notifications
2025-07-31 07:48:22 - Starting notification fetch
2025-07-31 13:48:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:48:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:48:22 - Found 0 notifications
2025-07-31 07:48:32 - Starting notification fetch
2025-07-31 13:48:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:48:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:48:32 - Found 0 notifications
2025-07-31 07:48:50 - Starting notification fetch
2025-07-31 13:48:50 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:48:50 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:48:50 - Found 0 notifications
2025-07-31 07:48:52 - Starting notification fetch
2025-07-31 13:48:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:48:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:48:52 - Found 0 notifications
2025-07-31 07:49:02 - Starting notification fetch
2025-07-31 13:49:02 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:49:02 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:49:02 - Found 0 notifications
2025-07-31 07:49:20 - Starting notification fetch
2025-07-31 13:49:20 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:49:20 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:49:20 - Found 0 notifications
2025-07-31 07:49:22 - Starting notification fetch
2025-07-31 13:49:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:49:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:49:22 - Found 0 notifications
2025-07-31 07:49:32 - Starting notification fetch
2025-07-31 13:49:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:49:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:49:32 - Found 0 notifications
2025-07-31 07:49:50 - Starting notification fetch
2025-07-31 13:49:50 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:49:50 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:49:50 - Found 0 notifications
2025-07-31 07:50:05 - Starting notification fetch
2025-07-31 13:50:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:50:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:50:06 - Found 0 notifications
2025-07-31 07:50:09 - Starting notification fetch
2025-07-31 13:50:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:50:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:50:09 - Found 0 notifications
2025-07-31 07:50:20 - Starting notification fetch
2025-07-31 13:50:20 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:50:20 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:50:20 - Found 0 notifications
2025-07-31 07:50:24 - Starting notification fetch
2025-07-31 13:50:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:50:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:50:25 - Found 0 notifications
2025-07-31 07:50:35 - Starting notification fetch
2025-07-31 13:50:35 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:50:35 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:50:35 - Found 0 notifications
2025-07-31 07:50:39 - Starting notification fetch
2025-07-31 13:50:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:50:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:50:39 - Found 0 notifications
2025-07-31 07:50:50 - Starting notification fetch
2025-07-31 13:50:50 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:50:50 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:50:50 - Found 0 notifications
2025-07-31 07:50:54 - Starting notification fetch
2025-07-31 13:50:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:50:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:50:55 - Found 0 notifications
2025-07-31 07:51:06 - Starting notification fetch
2025-07-31 13:51:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:51:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:51:06 - Found 0 notifications
2025-07-31 07:51:09 - Starting notification fetch
2025-07-31 13:51:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:51:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:51:09 - Found 0 notifications
2025-07-31 07:51:20 - Starting notification fetch
2025-07-31 13:51:20 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:51:20 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:51:20 - Found 0 notifications
2025-07-31 07:51:24 - Starting notification fetch
2025-07-31 13:51:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:51:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:51:25 - Found 0 notifications
2025-07-31 07:51:36 - Starting notification fetch
2025-07-31 13:51:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:51:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:51:36 - Found 0 notifications
2025-07-31 07:51:39 - Starting notification fetch
2025-07-31 13:51:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:51:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:51:39 - Found 0 notifications
2025-07-31 07:51:50 - Starting notification fetch
2025-07-31 13:51:50 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:51:50 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:51:50 - Found 0 notifications
2025-07-31 07:51:55 - Starting notification fetch
2025-07-31 13:51:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:51:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:51:55 - Found 0 notifications
2025-07-31 07:52:01 - Starting notification fetch
2025-07-31 13:52:01 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:52:01 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:52:01 - Found 0 notifications
2025-07-31 07:52:07 - Starting notification fetch
2025-07-31 13:52:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:52:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:52:07 - Found 0 notifications
2025-07-31 07:52:09 - Starting notification fetch
2025-07-31 13:52:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:52:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:52:09 - Found 0 notifications
2025-07-31 07:52:21 - Starting notification fetch
2025-07-31 13:52:21 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:52:21 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:52:21 - Found 0 notifications
2025-07-31 07:52:25 - Starting notification fetch
2025-07-31 13:52:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:52:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:52:25 - Found 0 notifications
2025-07-31 07:52:32 - Starting notification fetch
2025-07-31 13:52:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:52:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:52:32 - Found 0 notifications
2025-07-31 07:52:37 - Starting notification fetch
2025-07-31 13:52:37 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:52:37 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:52:37 - Found 0 notifications
2025-07-31 07:52:39 - Starting notification fetch
2025-07-31 13:52:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:52:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:52:39 - Found 0 notifications
2025-07-31 07:52:51 - Starting notification fetch
2025-07-31 13:52:51 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:52:51 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:52:51 - Found 0 notifications
2025-07-31 07:52:55 - Starting notification fetch
2025-07-31 13:52:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:52:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:52:55 - Found 0 notifications
2025-07-31 07:53:02 - Starting notification fetch
2025-07-31 13:53:02 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:53:02 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:53:02 - Found 0 notifications
2025-07-31 07:53:07 - Starting notification fetch
2025-07-31 13:53:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:53:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:53:07 - Found 0 notifications
2025-07-31 07:53:09 - Starting notification fetch
2025-07-31 13:53:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:53:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:53:09 - Found 0 notifications
2025-07-31 07:53:22 - Starting notification fetch
2025-07-31 13:53:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:53:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:53:22 - Found 0 notifications
2025-07-31 07:53:25 - Starting notification fetch
2025-07-31 13:53:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:53:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:53:25 - Found 0 notifications
2025-07-31 07:53:32 - Starting notification fetch
2025-07-31 13:53:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:53:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:53:32 - Found 0 notifications
2025-07-31 07:53:37 - Starting notification fetch
2025-07-31 13:53:37 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:53:37 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:53:37 - Found 0 notifications
2025-07-31 07:53:39 - Starting notification fetch
2025-07-31 13:53:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:53:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:53:39 - Found 0 notifications
2025-07-31 07:53:41 - Starting notification fetch
2025-07-31 13:53:41 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:53:41 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:53:41 - Found 0 notifications
2025-07-31 07:53:52 - Starting notification fetch
2025-07-31 13:53:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:53:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:53:52 - Found 0 notifications
2025-07-31 07:53:55 - Starting notification fetch
2025-07-31 13:53:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:53:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:53:55 - Found 0 notifications
2025-07-31 07:54:03 - Starting notification fetch
2025-07-31 13:54:03 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:54:03 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:54:03 - Found 0 notifications
2025-07-31 07:54:08 - Starting notification fetch
2025-07-31 13:54:08 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:54:08 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:54:08 - Found 0 notifications
2025-07-31 07:54:09 - Starting notification fetch
2025-07-31 13:54:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:54:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:54:09 - Found 0 notifications
2025-07-31 07:54:11 - Starting notification fetch
2025-07-31 13:54:11 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:54:11 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:54:11 - Found 0 notifications
2025-07-31 07:54:22 - Starting notification fetch
2025-07-31 13:54:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:54:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:54:22 - Found 0 notifications
2025-07-31 07:54:25 - Starting notification fetch
2025-07-31 13:54:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:54:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:54:25 - Found 0 notifications
2025-07-31 07:54:32 - Starting notification fetch
2025-07-31 13:54:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:54:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:54:32 - Found 0 notifications
2025-07-31 07:54:33 - Starting notification fetch
2025-07-31 13:54:34 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:54:34 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:54:34 - Found 0 notifications
2025-07-31 07:54:38 - Starting notification fetch
2025-07-31 13:54:38 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:54:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:54:39 - Found 0 notifications
2025-07-31 07:54:40 - Starting notification fetch
2025-07-31 13:54:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:54:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:54:40 - Found 0 notifications
2025-07-31 07:54:42 - Starting notification fetch
2025-07-31 13:54:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:54:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:54:42 - Found 0 notifications
2025-07-31 07:54:53 - Starting notification fetch
2025-07-31 13:54:53 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:54:53 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:54:53 - Found 0 notifications
2025-07-31 07:54:55 - Starting notification fetch
2025-07-31 13:54:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:54:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:54:55 - Found 0 notifications
2025-07-31 07:55:02 - Starting notification fetch
2025-07-31 13:55:02 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:02 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:03 - Found 0 notifications
2025-07-31 07:55:03 - Starting notification fetch
2025-07-31 13:55:03 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:04 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:04 - Found 0 notifications
2025-07-31 07:55:09 - Starting notification fetch
2025-07-31 13:55:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:09 - Found 0 notifications
2025-07-31 07:55:10 - Starting notification fetch
2025-07-31 13:55:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:10 - Found 0 notifications
2025-07-31 07:55:12 - Starting notification fetch
2025-07-31 13:55:12 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:12 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:12 - Found 0 notifications
2025-07-31 07:55:23 - Starting notification fetch
2025-07-31 13:55:23 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:23 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:23 - Found 0 notifications
2025-07-31 07:55:25 - Starting notification fetch
2025-07-31 13:55:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:25 - Found 0 notifications
2025-07-31 07:55:34 - Starting notification fetch
2025-07-31 13:55:34 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:34 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:34 - Found 0 notifications
2025-07-31 07:55:39 - Starting notification fetch
2025-07-31 13:55:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:39 - Found 0 notifications
2025-07-31 07:55:40 - Starting notification fetch
2025-07-31 13:55:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:40 - Found 0 notifications
2025-07-31 07:55:42 - Starting notification fetch
2025-07-31 13:55:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:42 - Found 0 notifications
2025-07-31 07:55:46 - Starting notification fetch
2025-07-31 13:55:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:46 - Found 0 notifications
2025-07-31 07:55:53 - Starting notification fetch
2025-07-31 13:55:53 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:53 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:53 - Found 0 notifications
2025-07-31 07:55:55 - Starting notification fetch
2025-07-31 13:55:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:55:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:55:55 - Found 0 notifications
2025-07-31 07:56:04 - Starting notification fetch
2025-07-31 13:56:04 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:56:04 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:56:04 - Found 0 notifications
2025-07-31 07:56:09 - Starting notification fetch
2025-07-31 13:56:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:56:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:56:09 - Found 0 notifications
2025-07-31 07:56:10 - Starting notification fetch
2025-07-31 13:56:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:56:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:56:10 - Found 0 notifications
2025-07-31 07:56:11 - Starting notification fetch
2025-07-31 13:56:11 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:56:11 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:56:11 - Found 0 notifications
2025-07-31 07:56:13 - Starting notification fetch
2025-07-31 13:56:13 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:56:13 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:56:13 - Found 0 notifications
2025-07-31 07:56:16 - Starting notification fetch
2025-07-31 13:56:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:56:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:56:16 - Found 0 notifications
2025-07-31 07:56:24 - Starting notification fetch
2025-07-31 13:56:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:56:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:56:24 - Found 0 notifications
2025-07-31 07:56:25 - Starting notification fetch
2025-07-31 13:56:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:56:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:56:25 - Found 0 notifications
2025-07-31 07:56:39 - Starting notification fetch
2025-07-31 13:56:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:56:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:56:39 - Found 0 notifications
2025-07-31 07:56:41 - Starting notification fetch
2025-07-31 13:56:41 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:56:41 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:56:41 - Found 0 notifications
2025-07-31 07:56:46 - Starting notification fetch
2025-07-31 13:56:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:56:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:56:46 - Found 0 notifications
2025-07-31 07:56:54 - Starting notification fetch
2025-07-31 13:56:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:56:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:56:54 - Found 0 notifications
2025-07-31 07:56:55 - Starting notification fetch
2025-07-31 13:56:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:56:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:56:55 - Found 0 notifications
2025-07-31 07:57:08 - Starting notification fetch
2025-07-31 13:57:08 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:57:08 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:57:08 - Found 0 notifications
2025-07-31 07:57:09 - Starting notification fetch
2025-07-31 13:57:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:57:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:57:09 - Found 0 notifications
2025-07-31 07:57:11 - Starting notification fetch
2025-07-31 13:57:11 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:57:11 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:57:11 - Found 0 notifications
2025-07-31 07:57:16 - Starting notification fetch
2025-07-31 13:57:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:57:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:57:16 - Found 0 notifications
2025-07-31 07:57:24 - Starting notification fetch
2025-07-31 13:57:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:57:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:57:24 - Found 0 notifications
2025-07-31 07:57:25 - Starting notification fetch
2025-07-31 13:57:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:57:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:57:25 - Found 0 notifications
2025-07-31 07:57:38 - Starting notification fetch
2025-07-31 13:57:38 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:57:38 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:57:38 - Found 0 notifications
2025-07-31 07:57:39 - Starting notification fetch
2025-07-31 13:57:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:57:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:57:39 - Found 0 notifications
2025-07-31 07:57:41 - Starting notification fetch
2025-07-31 13:57:41 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:57:41 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:57:41 - Found 0 notifications
2025-07-31 07:57:46 - Starting notification fetch
2025-07-31 13:57:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:57:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:57:46 - Found 0 notifications
2025-07-31 07:57:54 - Starting notification fetch
2025-07-31 13:57:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:57:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:57:54 - Found 0 notifications
2025-07-31 07:58:05 - Starting notification fetch
2025-07-31 13:58:05 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:58:05 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:58:05 - Found 0 notifications
2025-07-31 07:58:08 - Starting notification fetch
2025-07-31 13:58:08 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:58:08 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:58:08 - Found 0 notifications
2025-07-31 07:58:09 - Starting notification fetch
2025-07-31 13:58:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:58:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:58:09 - Found 0 notifications
2025-07-31 07:58:11 - Starting notification fetch
2025-07-31 13:58:11 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:58:11 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:58:11 - Found 0 notifications
2025-07-31 07:58:16 - Starting notification fetch
2025-07-31 13:58:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:58:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:58:16 - Found 0 notifications
2025-07-31 07:58:24 - Starting notification fetch
2025-07-31 13:58:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:58:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:58:24 - Found 0 notifications
2025-07-31 07:58:35 - Starting notification fetch
2025-07-31 13:58:35 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:58:35 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:58:35 - Found 0 notifications
2025-07-31 07:58:38 - Starting notification fetch
2025-07-31 13:58:38 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:58:38 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:58:38 - Found 0 notifications
2025-07-31 07:58:39 - Starting notification fetch
2025-07-31 13:58:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:58:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:58:39 - Found 0 notifications
2025-07-31 07:58:42 - Starting notification fetch
2025-07-31 13:58:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:58:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:58:42 - Found 0 notifications
2025-07-31 07:58:46 - Starting notification fetch
2025-07-31 13:58:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:58:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:58:46 - Found 0 notifications
2025-07-31 07:58:54 - Starting notification fetch
2025-07-31 13:58:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:58:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:58:54 - Found 0 notifications
2025-07-31 07:59:05 - Starting notification fetch
2025-07-31 13:59:05 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:59:05 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:59:05 - Found 0 notifications
2025-07-31 07:59:08 - Starting notification fetch
2025-07-31 13:59:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:59:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:59:09 - Found 0 notifications
2025-07-31 07:59:09 - Starting notification fetch
2025-07-31 13:59:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:59:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:59:10 - Found 0 notifications
2025-07-31 07:59:12 - Starting notification fetch
2025-07-31 13:59:12 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:59:12 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:59:12 - Found 0 notifications
2025-07-31 07:59:24 - Starting notification fetch
2025-07-31 13:59:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:59:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:59:24 - Found 0 notifications
2025-07-31 07:59:35 - Starting notification fetch
2025-07-31 13:59:35 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:59:35 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:59:35 - Found 0 notifications
2025-07-31 07:59:36 - Starting notification fetch
2025-07-31 13:59:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:59:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:59:36 - Found 0 notifications
2025-07-31 07:59:38 - Starting notification fetch
2025-07-31 13:59:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:59:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:59:39 - Found 0 notifications
2025-07-31 07:59:39 - Starting notification fetch
2025-07-31 13:59:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:59:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:59:40 - Found 0 notifications
2025-07-31 07:59:40 - Starting notification fetch
2025-07-31 13:59:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:59:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:59:40 - Found 0 notifications
2025-07-31 07:59:42 - Starting notification fetch
2025-07-31 13:59:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:59:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:59:42 - Found 0 notifications
2025-07-31 07:59:54 - Starting notification fetch
2025-07-31 13:59:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 13:59:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 13:59:54 - Found 0 notifications
2025-07-31 08:00:05 - Starting notification fetch
2025-07-31 14:00:05 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:05 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:05 - Found 0 notifications
2025-07-31 08:00:06 - Starting notification fetch
2025-07-31 14:00:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:06 - Found 0 notifications
2025-07-31 08:00:10 - Starting notification fetch
2025-07-31 14:00:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:10 - Found 0 notifications
2025-07-31 08:00:14 - Starting notification fetch
2025-07-31 08:00:14 - Starting notification fetch
2025-07-31 08:00:14 - Starting notification fetch
2025-07-31 14:00:14 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:14 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:14 - Found 0 notifications
2025-07-31 14:00:14 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:14 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:14 - Found 0 notifications
2025-07-31 14:00:14 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:14 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:14 - Found 0 notifications
2025-07-31 08:00:24 - Starting notification fetch
2025-07-31 14:00:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:24 - Found 0 notifications
2025-07-31 08:00:35 - Starting notification fetch
2025-07-31 14:00:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:36 - Found 0 notifications
2025-07-31 08:00:37 - Starting notification fetch
2025-07-31 14:00:37 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:37 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:37 - Found 0 notifications
2025-07-31 08:00:41 - Starting notification fetch
2025-07-31 14:00:41 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:41 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:41 - Found 0 notifications
2025-07-31 08:00:44 - Starting notification fetch
2025-07-31 14:00:44 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:44 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:44 - Found 0 notifications
2025-07-31 08:00:44 - Starting notification fetch
2025-07-31 08:00:44 - Starting notification fetch
2025-07-31 14:00:44 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:44 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:44 - Found 0 notifications
2025-07-31 14:00:44 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:44 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:44 - Found 0 notifications
2025-07-31 08:00:54 - Starting notification fetch
2025-07-31 14:00:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:00:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:00:54 - Found 0 notifications
2025-07-31 08:01:06 - Starting notification fetch
2025-07-31 14:01:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:06 - Found 0 notifications
2025-07-31 08:01:07 - Starting notification fetch
2025-07-31 14:01:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:07 - Found 0 notifications
2025-07-31 08:01:11 - Starting notification fetch
2025-07-31 14:01:11 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:11 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:11 - Found 0 notifications
2025-07-31 08:01:14 - Starting notification fetch
2025-07-31 08:01:14 - Starting notification fetch
2025-07-31 14:01:14 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:14 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:14 - Found 0 notifications
2025-07-31 14:01:14 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:14 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:14 - Found 0 notifications
2025-07-31 08:01:14 - Starting notification fetch
2025-07-31 14:01:14 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:14 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:14 - Found 0 notifications
2025-07-31 08:01:24 - Starting notification fetch
2025-07-31 14:01:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:24 - Found 0 notifications
2025-07-31 08:01:36 - Starting notification fetch
2025-07-31 14:01:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:36 - Found 0 notifications
2025-07-31 08:01:37 - Starting notification fetch
2025-07-31 14:01:37 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:37 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:37 - Found 0 notifications
2025-07-31 08:01:41 - Starting notification fetch
2025-07-31 14:01:41 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:41 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:41 - Found 0 notifications
2025-07-31 08:01:44 - Starting notification fetch
2025-07-31 08:01:44 - Starting notification fetch
2025-07-31 14:01:44 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:44 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:44 - Found 0 notifications
2025-07-31 14:01:44 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:44 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:44 - Found 0 notifications
2025-07-31 08:01:45 - Starting notification fetch
2025-07-31 14:01:45 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:45 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:45 - Found 0 notifications
2025-07-31 08:01:54 - Starting notification fetch
2025-07-31 14:01:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:01:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:01:54 - Found 0 notifications
2025-07-31 08:02:06 - Starting notification fetch
2025-07-31 14:02:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:02:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:02:06 - Found 0 notifications
2025-07-31 08:02:07 - Starting notification fetch
2025-07-31 14:02:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:02:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:02:07 - Found 0 notifications
2025-07-31 08:02:14 - Starting notification fetch
2025-07-31 08:02:14 - Starting notification fetch
2025-07-31 14:02:14 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:02:14 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:02:14 - Found 0 notifications
2025-07-31 14:02:14 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:02:14 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:02:14 - Found 0 notifications
2025-07-31 08:02:15 - Starting notification fetch
2025-07-31 14:02:15 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:02:15 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:02:15 - Found 0 notifications
2025-07-31 08:02:24 - Starting notification fetch
2025-07-31 14:02:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:02:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:02:24 - Found 0 notifications
2025-07-31 08:02:36 - Starting notification fetch
2025-07-31 14:02:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:02:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:02:36 - Found 0 notifications
2025-07-31 08:02:37 - Starting notification fetch
2025-07-31 14:02:37 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:02:37 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:02:37 - Found 0 notifications
2025-07-31 08:02:39 - Starting notification fetch
2025-07-31 14:02:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:02:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:02:39 - Found 0 notifications
2025-07-31 08:02:44 - Starting notification fetch
2025-07-31 08:02:44 - Starting notification fetch
2025-07-31 14:02:44 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:02:44 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:02:44 - Found 0 notifications
2025-07-31 14:02:44 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:02:44 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:02:44 - Found 0 notifications
2025-07-31 08:02:45 - Starting notification fetch
2025-07-31 14:02:45 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:02:45 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:02:45 - Found 0 notifications
2025-07-31 08:02:55 - Starting notification fetch
2025-07-31 14:02:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:02:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:02:55 - Found 0 notifications
2025-07-31 08:03:07 - Starting notification fetch
2025-07-31 08:03:07 - Starting notification fetch
2025-07-31 14:03:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:07 - Found 0 notifications
2025-07-31 14:03:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:07 - Found 0 notifications
2025-07-31 08:03:09 - Starting notification fetch
2025-07-31 14:03:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:09 - Found 0 notifications
2025-07-31 08:03:12 - Starting notification fetch
2025-07-31 14:03:12 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:12 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:12 - Found 0 notifications
2025-07-31 08:03:14 - Starting notification fetch
2025-07-31 08:03:14 - Starting notification fetch
2025-07-31 14:03:14 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:14 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:14 - Found 0 notifications
2025-07-31 14:03:15 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:15 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:15 - Found 0 notifications
2025-07-31 08:03:15 - Starting notification fetch
2025-07-31 14:03:15 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:15 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:15 - Found 0 notifications
2025-07-31 08:03:25 - Starting notification fetch
2025-07-31 14:03:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:25 - Found 0 notifications
2025-07-31 08:03:37 - Starting notification fetch
2025-07-31 14:03:38 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:38 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:38 - Found 0 notifications
2025-07-31 08:03:38 - Starting notification fetch
2025-07-31 14:03:38 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:38 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:38 - Found 0 notifications
2025-07-31 08:03:39 - Starting notification fetch
2025-07-31 14:03:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:39 - Found 0 notifications
2025-07-31 08:03:42 - Starting notification fetch
2025-07-31 14:03:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:42 - Found 0 notifications
2025-07-31 08:03:42 - Starting notification fetch
2025-07-31 14:03:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:43 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:43 - Found 0 notifications
2025-07-31 08:03:44 - Starting notification fetch
2025-07-31 08:03:44 - Starting notification fetch
2025-07-31 14:03:45 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:45 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:45 - Found 0 notifications
2025-07-31 14:03:45 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:45 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:45 - Found 0 notifications
2025-07-31 08:03:45 - Starting notification fetch
2025-07-31 14:03:45 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:45 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:45 - Found 0 notifications
2025-07-31 08:03:55 - Starting notification fetch
2025-07-31 14:03:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:03:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:03:55 - Found 0 notifications
2025-07-31 08:04:08 - Starting notification fetch
2025-07-31 14:04:08 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:08 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:08 - Found 0 notifications
2025-07-31 08:04:08 - Starting notification fetch
2025-07-31 14:04:08 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:08 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:08 - Found 0 notifications
2025-07-31 08:04:09 - Starting notification fetch
2025-07-31 14:04:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:09 - Found 0 notifications
2025-07-31 08:04:12 - Starting notification fetch
2025-07-31 14:04:12 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:12 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:12 - Found 0 notifications
2025-07-31 08:04:12 - Starting notification fetch
2025-07-31 14:04:13 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:13 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:13 - Found 0 notifications
2025-07-31 08:04:14 - Starting notification fetch
2025-07-31 08:04:14 - Starting notification fetch
2025-07-31 14:04:15 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:15 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:15 - Found 0 notifications
2025-07-31 14:04:15 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:15 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:15 - Found 0 notifications
2025-07-31 08:04:15 - Starting notification fetch
2025-07-31 14:04:15 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:15 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:15 - Found 0 notifications
2025-07-31 08:04:25 - Starting notification fetch
2025-07-31 14:04:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:25 - Found 0 notifications
2025-07-31 08:04:38 - Starting notification fetch
2025-07-31 14:04:38 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:38 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:38 - Found 0 notifications
2025-07-31 08:04:38 - Starting notification fetch
2025-07-31 14:04:38 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:38 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:38 - Found 0 notifications
2025-07-31 08:04:39 - Starting notification fetch
2025-07-31 14:04:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:40 - Found 0 notifications
2025-07-31 08:04:43 - Starting notification fetch
2025-07-31 14:04:43 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:43 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:43 - Found 0 notifications
2025-07-31 08:04:45 - Starting notification fetch
2025-07-31 08:04:45 - Starting notification fetch
2025-07-31 14:04:45 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:45 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:45 - Found 0 notifications
2025-07-31 14:04:45 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:45 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:45 - Found 0 notifications
2025-07-31 08:04:49 - Starting notification fetch
2025-07-31 14:04:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:49 - Found 0 notifications
2025-07-31 08:04:50 - Starting notification fetch
2025-07-31 14:04:50 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:50 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:50 - Found 0 notifications
2025-07-31 08:04:55 - Starting notification fetch
2025-07-31 14:04:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:04:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:04:55 - Found 0 notifications
2025-07-31 08:05:08 - Starting notification fetch
2025-07-31 14:05:08 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:08 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:08 - Found 0 notifications
2025-07-31 08:05:09 - Starting notification fetch
2025-07-31 14:05:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:09 - Found 0 notifications
2025-07-31 08:05:10 - Starting notification fetch
2025-07-31 14:05:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:10 - Found 0 notifications
2025-07-31 08:05:13 - Starting notification fetch
2025-07-31 14:05:13 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:13 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:13 - Found 0 notifications
2025-07-31 08:05:15 - Starting notification fetch
2025-07-31 08:05:15 - Starting notification fetch
2025-07-31 14:05:15 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:15 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:15 - Found 0 notifications
2025-07-31 14:05:15 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:15 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:15 - Found 0 notifications
2025-07-31 08:05:19 - Starting notification fetch
2025-07-31 14:05:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:19 - Found 0 notifications
2025-07-31 08:05:20 - Starting notification fetch
2025-07-31 14:05:20 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:20 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:20 - Found 0 notifications
2025-07-31 08:05:20 - Starting notification fetch
2025-07-31 14:05:20 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:20 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:20 - Found 0 notifications
2025-07-31 08:05:25 - Starting notification fetch
2025-07-31 14:05:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:25 - Found 0 notifications
2025-07-31 08:05:38 - Starting notification fetch
2025-07-31 14:05:38 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:38 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:38 - Found 0 notifications
2025-07-31 08:05:39 - Starting notification fetch
2025-07-31 14:05:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:39 - Found 0 notifications
2025-07-31 08:05:40 - Starting notification fetch
2025-07-31 14:05:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:40 - Found 0 notifications
2025-07-31 08:05:43 - Starting notification fetch
2025-07-31 14:05:43 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:43 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:43 - Found 0 notifications
2025-07-31 08:05:45 - Starting notification fetch
2025-07-31 08:05:45 - Starting notification fetch
2025-07-31 14:05:45 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:45 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:45 - Found 0 notifications
2025-07-31 14:05:45 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:45 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:45 - Found 0 notifications
2025-07-31 08:05:50 - Starting notification fetch
2025-07-31 14:05:50 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:50 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:50 - Found 0 notifications
2025-07-31 08:05:50 - Starting notification fetch
2025-07-31 14:05:50 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:50 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:50 - Found 0 notifications
2025-07-31 08:05:50 - Starting notification fetch
2025-07-31 14:05:50 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:50 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:50 - Found 0 notifications
2025-07-31 08:05:55 - Starting notification fetch
2025-07-31 14:05:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:05:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:05:55 - Found 0 notifications
2025-07-31 08:06:08 - Starting notification fetch
2025-07-31 14:06:08 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:08 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:08 - Found 0 notifications
2025-07-31 08:06:10 - Starting notification fetch
2025-07-31 08:06:10 - Starting notification fetch
2025-07-31 14:06:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:10 - Found 0 notifications
2025-07-31 14:06:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:10 - Found 0 notifications
2025-07-31 08:06:14 - Starting notification fetch
2025-07-31 14:06:14 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:14 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:14 - Found 0 notifications
2025-07-31 08:06:15 - Starting notification fetch
2025-07-31 08:06:15 - Starting notification fetch
2025-07-31 14:06:15 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:15 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:15 - Found 0 notifications
2025-07-31 14:06:15 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:15 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:15 - Found 0 notifications
2025-07-31 08:06:20 - Starting notification fetch
2025-07-31 14:06:20 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:20 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:20 - Found 0 notifications
2025-07-31 08:06:20 - Starting notification fetch
2025-07-31 14:06:20 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:20 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:20 - Found 0 notifications
2025-07-31 08:06:21 - Starting notification fetch
2025-07-31 14:06:21 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:21 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:21 - Found 0 notifications
2025-07-31 08:06:25 - Starting notification fetch
2025-07-31 14:06:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:25 - Found 0 notifications
2025-07-31 08:06:36 - Starting notification fetch
2025-07-31 14:06:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:36 - Found 0 notifications
2025-07-31 08:06:39 - Starting notification fetch
2025-07-31 14:06:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:39 - Found 0 notifications
2025-07-31 08:06:40 - Starting notification fetch
2025-07-31 08:06:40 - Starting notification fetch
2025-07-31 14:06:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:40 - Found 0 notifications
2025-07-31 14:06:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:40 - Found 0 notifications
2025-07-31 08:06:44 - Starting notification fetch
2025-07-31 14:06:44 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:44 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:44 - Found 0 notifications
2025-07-31 08:06:45 - Starting notification fetch
2025-07-31 08:06:45 - Starting notification fetch
2025-07-31 14:06:45 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:45 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:45 - Found 0 notifications
2025-07-31 14:06:45 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:45 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:45 - Found 0 notifications
2025-07-31 08:06:50 - Starting notification fetch
2025-07-31 08:06:50 - Starting notification fetch
2025-07-31 14:06:50 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:50 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:50 - Found 0 notifications
2025-07-31 14:06:51 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:51 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:51 - Found 0 notifications
2025-07-31 08:06:51 - Starting notification fetch
2025-07-31 14:06:51 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:51 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:51 - Found 0 notifications
2025-07-31 08:06:55 - Starting notification fetch
2025-07-31 14:06:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:06:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:06:55 - Found 0 notifications
2025-07-31 08:07:09 - Starting notification fetch
2025-07-31 14:07:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:09 - Found 0 notifications
2025-07-31 08:07:10 - Starting notification fetch
2025-07-31 08:07:10 - Starting notification fetch
2025-07-31 14:07:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:10 - Found 0 notifications
2025-07-31 14:07:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:10 - Found 0 notifications
2025-07-31 08:07:14 - Starting notification fetch
2025-07-31 14:07:14 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:14 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:14 - Found 0 notifications
2025-07-31 08:07:15 - Starting notification fetch
2025-07-31 14:07:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:16 - Found 0 notifications
2025-07-31 08:07:17 - Starting notification fetch
2025-07-31 14:07:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:17 - Found 0 notifications
2025-07-31 08:07:20 - Starting notification fetch
2025-07-31 14:07:21 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:21 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:21 - Found 0 notifications
2025-07-31 08:07:21 - Starting notification fetch
2025-07-31 14:07:21 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:21 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:21 - Found 0 notifications
2025-07-31 08:07:22 - Starting notification fetch
2025-07-31 14:07:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:22 - Found 0 notifications
2025-07-31 08:07:25 - Starting notification fetch
2025-07-31 14:07:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:25 - Found 0 notifications
2025-07-31 08:07:39 - Starting notification fetch
2025-07-31 14:07:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:39 - Found 0 notifications
2025-07-31 08:07:40 - Starting notification fetch
2025-07-31 08:07:40 - Starting notification fetch
2025-07-31 14:07:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:40 - Found 0 notifications
2025-07-31 14:07:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:40 - Found 0 notifications
2025-07-31 08:07:45 - Starting notification fetch
2025-07-31 14:07:45 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:45 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:45 - Found 0 notifications
2025-07-31 08:07:46 - Starting notification fetch
2025-07-31 14:07:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:46 - Found 0 notifications
2025-07-31 08:07:47 - Starting notification fetch
2025-07-31 14:07:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:47 - Found 0 notifications
2025-07-31 08:07:51 - Starting notification fetch
2025-07-31 08:07:51 - Starting notification fetch
2025-07-31 14:07:51 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:51 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:51 - Found 0 notifications
2025-07-31 14:07:51 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:51 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:51 - Found 0 notifications
2025-07-31 08:07:52 - Starting notification fetch
2025-07-31 14:07:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:52 - Found 0 notifications
2025-07-31 08:07:55 - Starting notification fetch
2025-07-31 14:07:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:07:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:07:55 - Found 0 notifications
2025-07-31 08:08:09 - Starting notification fetch
2025-07-31 14:08:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:09 - Found 0 notifications
2025-07-31 08:08:10 - Starting notification fetch
2025-07-31 08:08:10 - Starting notification fetch
2025-07-31 14:08:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:10 - Found 0 notifications
2025-07-31 14:08:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:10 - Found 0 notifications
2025-07-31 08:08:15 - Starting notification fetch
2025-07-31 14:08:15 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:15 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:15 - Found 0 notifications
2025-07-31 08:08:16 - Starting notification fetch
2025-07-31 08:08:16 - Starting notification fetch
2025-07-31 14:08:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:16 - Found 0 notifications
2025-07-31 14:08:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:16 - Found 0 notifications
2025-07-31 08:08:21 - Starting notification fetch
2025-07-31 14:08:21 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:21 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:21 - Found 0 notifications
2025-07-31 08:08:21 - Starting notification fetch
2025-07-31 14:08:21 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:21 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:21 - Found 0 notifications
2025-07-31 08:08:22 - Starting notification fetch
2025-07-31 14:08:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:22 - Found 0 notifications
2025-07-31 08:08:25 - Starting notification fetch
2025-07-31 14:08:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:25 - Found 0 notifications
2025-07-31 08:08:28 - Starting notification fetch
2025-07-31 14:08:28 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:28 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:28 - Found 0 notifications
2025-07-31 08:08:39 - Starting notification fetch
2025-07-31 14:08:39 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:39 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:39 - Found 0 notifications
2025-07-31 08:08:40 - Starting notification fetch
2025-07-31 08:08:40 - Starting notification fetch
2025-07-31 14:08:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:40 - Found 0 notifications
2025-07-31 14:08:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:40 - Found 0 notifications
2025-07-31 08:08:46 - Starting notification fetch
2025-07-31 14:08:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 08:08:46 - Starting notification fetch
2025-07-31 14:08:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:46 - Found 0 notifications
2025-07-31 14:08:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:46 - Found 0 notifications
2025-07-31 08:08:46 - Starting notification fetch
2025-07-31 14:08:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:46 - Found 0 notifications
2025-07-31 08:08:51 - Starting notification fetch
2025-07-31 14:08:51 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:51 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:51 - Found 0 notifications
2025-07-31 08:08:52 - Starting notification fetch
2025-07-31 14:08:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:52 - Found 0 notifications
2025-07-31 08:08:52 - Starting notification fetch
2025-07-31 14:08:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:52 - Found 0 notifications
2025-07-31 08:08:55 - Starting notification fetch
2025-07-31 14:08:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:55 - Found 0 notifications
2025-07-31 08:08:58 - Starting notification fetch
2025-07-31 14:08:58 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:08:58 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:08:58 - Found 0 notifications
2025-07-31 08:09:09 - Starting notification fetch
2025-07-31 14:09:09 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:09 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:09 - Found 0 notifications
2025-07-31 08:09:10 - Starting notification fetch
2025-07-31 08:09:10 - Starting notification fetch
2025-07-31 14:09:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:10 - Found 0 notifications
2025-07-31 14:09:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:10 - Found 0 notifications
2025-07-31 08:09:16 - Starting notification fetch
2025-07-31 14:09:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:16 - Found 0 notifications
2025-07-31 08:09:16 - Starting notification fetch
2025-07-31 14:09:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:16 - Found 0 notifications
2025-07-31 08:09:16 - Starting notification fetch
2025-07-31 14:09:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:16 - Found 0 notifications
2025-07-31 08:09:21 - Starting notification fetch
2025-07-31 14:09:21 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:21 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:21 - Found 0 notifications
2025-07-31 08:09:22 - Starting notification fetch
2025-07-31 14:09:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:22 - Found 0 notifications
2025-07-31 08:09:22 - Starting notification fetch
2025-07-31 14:09:23 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:23 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:23 - Found 0 notifications
2025-07-31 08:09:25 - Starting notification fetch
2025-07-31 14:09:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:25 - Found 0 notifications
2025-07-31 08:09:28 - Starting notification fetch
2025-07-31 14:09:28 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:28 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:28 - Found 0 notifications
2025-07-31 08:09:39 - Starting notification fetch
2025-07-31 14:09:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:40 - Found 0 notifications
2025-07-31 08:09:40 - Starting notification fetch
2025-07-31 14:09:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:40 - Found 0 notifications
2025-07-31 08:09:40 - Starting notification fetch
2025-07-31 14:09:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:40 - Found 0 notifications
2025-07-31 08:09:41 - Starting notification fetch
2025-07-31 14:09:41 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:41 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:41 - Found 0 notifications
2025-07-31 08:09:46 - Starting notification fetch
2025-07-31 14:09:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:46 - Found 0 notifications
2025-07-31 08:09:46 - Starting notification fetch
2025-07-31 14:09:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:46 - Found 0 notifications
2025-07-31 08:09:47 - Starting notification fetch
2025-07-31 14:09:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:47 - Found 0 notifications
2025-07-31 08:09:51 - Starting notification fetch
2025-07-31 14:09:51 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:51 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:51 - Found 0 notifications
2025-07-31 08:09:52 - Starting notification fetch
2025-07-31 14:09:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:52 - Found 0 notifications
2025-07-31 08:09:52 - Starting notification fetch
2025-07-31 14:09:53 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:53 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:53 - Found 0 notifications
2025-07-31 08:09:56 - Starting notification fetch
2025-07-31 14:09:56 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:56 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:56 - Found 0 notifications
2025-07-31 08:09:58 - Starting notification fetch
2025-07-31 14:09:58 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:09:58 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:09:58 - Found 0 notifications
2025-07-31 08:10:06 - Starting notification fetch
2025-07-31 14:10:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:06 - Found 0 notifications
2025-07-31 08:10:09 - Starting notification fetch
2025-07-31 14:10:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:10 - Found 0 notifications
2025-07-31 08:10:10 - Starting notification fetch
2025-07-31 14:10:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:10 - Found 0 notifications
2025-07-31 08:10:11 - Starting notification fetch
2025-07-31 14:10:11 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:11 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:11 - Found 0 notifications
2025-07-31 08:10:16 - Starting notification fetch
2025-07-31 14:10:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:16 - Found 0 notifications
2025-07-31 08:10:16 - Starting notification fetch
2025-07-31 14:10:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:16 - Found 0 notifications
2025-07-31 08:10:17 - Starting notification fetch
2025-07-31 14:10:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:17 - Found 0 notifications
2025-07-31 08:10:21 - Starting notification fetch
2025-07-31 14:10:21 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:21 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:21 - Found 0 notifications
2025-07-31 08:10:24 - Starting notification fetch
2025-07-31 14:10:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:24 - Found 0 notifications
2025-07-31 08:10:26 - Starting notification fetch
2025-07-31 14:10:26 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:26 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:26 - Found 0 notifications
2025-07-31 08:10:28 - Starting notification fetch
2025-07-31 14:10:28 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:28 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:28 - Found 0 notifications
2025-07-31 08:10:39 - Starting notification fetch
2025-07-31 14:10:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:40 - Found 0 notifications
2025-07-31 08:10:40 - Starting notification fetch
2025-07-31 14:10:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:40 - Found 0 notifications
2025-07-31 08:10:41 - Starting notification fetch
2025-07-31 14:10:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:42 - Found 0 notifications
2025-07-31 08:10:46 - Starting notification fetch
2025-07-31 14:10:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:46 - Found 0 notifications
2025-07-31 08:10:46 - Starting notification fetch
2025-07-31 14:10:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:46 - Found 0 notifications
2025-07-31 08:10:47 - Starting notification fetch
2025-07-31 14:10:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:47 - Found 0 notifications
2025-07-31 08:10:52 - Starting notification fetch
2025-07-31 14:10:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:52 - Found 0 notifications
2025-07-31 08:10:57 - Starting notification fetch
2025-07-31 14:10:57 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:57 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:57 - Found 0 notifications
2025-07-31 08:10:58 - Starting notification fetch
2025-07-31 14:10:58 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:10:58 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:10:58 - Found 0 notifications
2025-07-31 08:11:09 - Starting notification fetch
2025-07-31 14:11:10 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:10 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:10 - Found 0 notifications
2025-07-31 08:11:10 - Starting notification fetch
2025-07-31 14:11:11 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:11 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:11 - Found 0 notifications
2025-07-31 08:11:11 - Starting notification fetch
2025-07-31 14:11:12 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:12 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:12 - Found 0 notifications
2025-07-31 08:11:16 - Starting notification fetch
2025-07-31 14:11:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:16 - Found 0 notifications
2025-07-31 08:11:16 - Starting notification fetch
2025-07-31 14:11:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:16 - Found 0 notifications
2025-07-31 08:11:17 - Starting notification fetch
2025-07-31 14:11:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:17 - Found 0 notifications
2025-07-31 08:11:22 - Starting notification fetch
2025-07-31 14:11:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:22 - Found 0 notifications
2025-07-31 08:11:27 - Starting notification fetch
2025-07-31 14:11:27 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:27 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:27 - Found 0 notifications
2025-07-31 08:11:29 - Starting notification fetch
2025-07-31 14:11:29 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:29 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:29 - Found 0 notifications
2025-07-31 08:11:40 - Starting notification fetch
2025-07-31 14:11:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:40 - Found 0 notifications
2025-07-31 08:11:40 - Starting notification fetch
2025-07-31 14:11:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:40 - Found 0 notifications
2025-07-31 08:11:42 - Starting notification fetch
2025-07-31 14:11:42 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:42 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:42 - Found 0 notifications
2025-07-31 08:11:46 - Starting notification fetch
2025-07-31 14:11:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:46 - Found 0 notifications
2025-07-31 08:11:46 - Starting notification fetch
2025-07-31 14:11:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:46 - Found 0 notifications
2025-07-31 08:11:47 - Starting notification fetch
2025-07-31 14:11:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:47 - Found 0 notifications
2025-07-31 08:11:52 - Starting notification fetch
2025-07-31 14:11:52 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:52 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:52 - Found 0 notifications
2025-07-31 08:11:59 - Starting notification fetch
2025-07-31 14:11:59 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:11:59 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:11:59 - Found 0 notifications
2025-07-31 08:12:06 - Starting notification fetch
2025-07-31 14:12:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:12:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:12:06 - Found 0 notifications
2025-07-31 08:12:13 - Starting notification fetch
2025-07-31 14:12:13 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:12:13 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:12:13 - Found 0 notifications
2025-07-31 08:12:16 - Starting notification fetch
2025-07-31 14:12:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:12:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:12:16 - Found 0 notifications
2025-07-31 08:12:17 - Starting notification fetch
2025-07-31 14:12:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:12:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:12:17 - Found 0 notifications
2025-07-31 08:12:22 - Starting notification fetch
2025-07-31 14:12:22 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:12:22 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:12:23 - Found 0 notifications
2025-07-31 08:12:29 - Starting notification fetch
2025-07-31 14:12:29 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:12:29 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:12:29 - Found 0 notifications
2025-07-31 08:12:30 - Starting notification fetch
2025-07-31 14:12:30 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:12:30 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:12:30 - Found 0 notifications
2025-07-31 08:12:36 - Starting notification fetch
2025-07-31 14:12:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:12:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:12:36 - Found 0 notifications
2025-07-31 08:12:43 - Starting notification fetch
2025-07-31 14:12:43 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:12:43 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:12:43 - Found 0 notifications
2025-07-31 08:12:47 - Starting notification fetch
2025-07-31 14:12:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:12:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:12:47 - Found 0 notifications
2025-07-31 08:12:47 - Starting notification fetch
2025-07-31 14:12:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:12:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:12:47 - Found 0 notifications
2025-07-31 08:12:52 - Starting notification fetch
2025-07-31 14:12:53 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:12:53 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:12:53 - Found 0 notifications
2025-07-31 08:12:59 - Starting notification fetch
2025-07-31 14:12:59 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:12:59 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:12:59 - Found 0 notifications
2025-07-31 08:13:00 - Starting notification fetch
2025-07-31 14:13:00 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:13:00 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:13:00 - Found 0 notifications
2025-07-31 08:13:06 - Starting notification fetch
2025-07-31 14:13:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:13:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:13:06 - Found 0 notifications
2025-07-31 08:13:13 - Starting notification fetch
2025-07-31 14:13:13 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:13:13 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:13:13 - Found 0 notifications
2025-07-31 08:13:17 - Starting notification fetch
2025-07-31 14:13:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:13:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:13:17 - Found 0 notifications
2025-07-31 08:13:18 - Starting notification fetch
2025-07-31 14:13:18 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:13:18 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:13:18 - Found 0 notifications
2025-07-31 08:13:22 - Starting notification fetch
2025-07-31 14:13:23 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:13:23 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:13:23 - Found 0 notifications
2025-07-31 08:13:30 - Starting notification fetch
2025-07-31 08:13:30 - Starting notification fetch
2025-07-31 14:13:30 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:13:30 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:13:30 - Found 0 notifications
2025-07-31 14:13:30 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:13:30 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:13:30 - Found 0 notifications
2025-07-31 08:13:36 - Starting notification fetch
2025-07-31 14:13:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:13:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:13:36 - Found 0 notifications
2025-07-31 08:13:43 - Starting notification fetch
2025-07-31 14:13:43 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:13:43 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:13:43 - Found 0 notifications
2025-07-31 08:13:47 - Starting notification fetch
2025-07-31 14:13:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:13:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:13:47 - Found 0 notifications
2025-07-31 08:13:48 - Starting notification fetch
2025-07-31 14:13:48 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:13:48 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:13:48 - Found 0 notifications
2025-07-31 08:13:52 - Starting notification fetch
2025-07-31 14:13:53 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:13:53 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:13:53 - Found 0 notifications
2025-07-31 08:14:00 - Starting notification fetch
2025-07-31 08:14:00 - Starting notification fetch
2025-07-31 14:14:00 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:00 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:00 - Found 0 notifications
2025-07-31 14:14:00 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:00 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:00 - Found 0 notifications
2025-07-31 08:14:06 - Starting notification fetch
2025-07-31 14:14:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:06 - Found 0 notifications
2025-07-31 08:14:13 - Starting notification fetch
2025-07-31 14:14:13 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:13 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:13 - Found 0 notifications
2025-07-31 08:14:17 - Starting notification fetch
2025-07-31 14:14:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:17 - Found 0 notifications
2025-07-31 08:14:18 - Starting notification fetch
2025-07-31 14:14:18 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:18 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:18 - Found 0 notifications
2025-07-31 08:14:22 - Starting notification fetch
2025-07-31 14:14:23 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:23 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:23 - Found 0 notifications
2025-07-31 08:14:30 - Starting notification fetch
2025-07-31 08:14:30 - Starting notification fetch
2025-07-31 14:14:30 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:30 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:30 - Found 0 notifications
2025-07-31 14:14:30 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:30 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:30 - Found 0 notifications
2025-07-31 08:14:36 - Starting notification fetch
2025-07-31 14:14:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:36 - Found 0 notifications
2025-07-31 08:14:43 - Starting notification fetch
2025-07-31 14:14:43 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:43 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:43 - Found 0 notifications
2025-07-31 08:14:47 - Starting notification fetch
2025-07-31 14:14:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:47 - Found 0 notifications
2025-07-31 08:14:48 - Starting notification fetch
2025-07-31 14:14:48 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:48 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:48 - Found 0 notifications
2025-07-31 08:14:53 - Starting notification fetch
2025-07-31 14:14:53 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:14:53 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:14:53 - Found 0 notifications
2025-07-31 08:15:00 - Starting notification fetch
2025-07-31 08:15:00 - Starting notification fetch
2025-07-31 14:15:00 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:00 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:00 - Found 0 notifications
2025-07-31 14:15:00 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:00 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:00 - Found 0 notifications
2025-07-31 08:15:06 - Starting notification fetch
2025-07-31 14:15:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:06 - Found 0 notifications
2025-07-31 08:15:16 - Starting notification fetch
2025-07-31 14:15:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:16 - Found 0 notifications
2025-07-31 08:15:18 - Starting notification fetch
2025-07-31 08:15:18 - Starting notification fetch
2025-07-31 14:15:18 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:18 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:18 - Found 0 notifications
2025-07-31 14:15:18 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:18 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:18 - Found 0 notifications
2025-07-31 08:15:24 - Starting notification fetch
2025-07-31 14:15:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:24 - Found 0 notifications
2025-07-31 08:15:30 - Starting notification fetch
2025-07-31 14:15:30 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:30 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:30 - Found 0 notifications
2025-07-31 08:15:31 - Starting notification fetch
2025-07-31 14:15:31 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:31 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:31 - Found 0 notifications
2025-07-31 08:15:36 - Starting notification fetch
2025-07-31 14:15:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:36 - Found 0 notifications
2025-07-31 08:15:46 - Starting notification fetch
2025-07-31 14:15:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:46 - Found 0 notifications
2025-07-31 08:15:48 - Starting notification fetch
2025-07-31 14:15:48 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:48 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:48 - Found 0 notifications
2025-07-31 08:15:48 - Starting notification fetch
2025-07-31 14:15:48 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:48 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:48 - Found 0 notifications
2025-07-31 08:15:54 - Starting notification fetch
2025-07-31 14:15:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:15:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:15:54 - Found 0 notifications
2025-07-31 08:16:00 - Starting notification fetch
2025-07-31 14:16:00 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:16:00 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:16:00 - Found 0 notifications
2025-07-31 08:16:01 - Starting notification fetch
2025-07-31 14:16:01 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:16:01 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:16:01 - Found 0 notifications
2025-07-31 08:16:06 - Starting notification fetch
2025-07-31 14:16:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:16:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:16:06 - Found 0 notifications
2025-07-31 08:16:16 - Starting notification fetch
2025-07-31 14:16:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:16:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:16:16 - Found 0 notifications
2025-07-31 08:16:18 - Starting notification fetch
2025-07-31 14:16:18 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:16:18 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:16:18 - Found 0 notifications
2025-07-31 08:16:18 - Starting notification fetch
2025-07-31 14:16:18 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:16:18 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:16:18 - Found 0 notifications
2025-07-31 08:16:24 - Starting notification fetch
2025-07-31 14:16:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:16:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:16:24 - Found 0 notifications
2025-07-31 08:16:30 - Starting notification fetch
2025-07-31 14:16:30 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:16:30 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:16:30 - Found 0 notifications
2025-07-31 08:16:31 - Starting notification fetch
2025-07-31 14:16:31 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:16:31 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:16:31 - Found 0 notifications
2025-07-31 08:16:36 - Starting notification fetch
2025-07-31 14:16:37 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:16:37 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:16:37 - Found 0 notifications
2025-07-31 08:16:46 - Starting notification fetch
2025-07-31 14:16:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:16:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:16:46 - Found 0 notifications
2025-07-31 08:16:49 - Starting notification fetch
2025-07-31 14:16:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:16:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:16:49 - Found 0 notifications
2025-07-31 08:16:54 - Starting notification fetch
2025-07-31 14:16:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:16:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:16:54 - Found 0 notifications
2025-07-31 08:17:01 - Starting notification fetch
2025-07-31 14:17:01 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:17:01 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:17:01 - Found 0 notifications
2025-07-31 08:17:06 - Starting notification fetch
2025-07-31 14:17:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:17:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:17:07 - Found 0 notifications
2025-07-31 08:17:16 - Starting notification fetch
2025-07-31 14:17:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:17:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:17:16 - Found 0 notifications
2025-07-31 08:17:16 - Starting notification fetch
2025-07-31 14:17:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:17:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:17:17 - Found 0 notifications
2025-07-31 08:17:19 - Starting notification fetch
2025-07-31 14:17:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:17:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:17:19 - Found 0 notifications
2025-07-31 08:17:24 - Starting notification fetch
2025-07-31 14:17:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:17:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:17:24 - Found 0 notifications
2025-07-31 08:17:32 - Starting notification fetch
2025-07-31 14:17:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:17:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:17:32 - Found 0 notifications
2025-07-31 08:17:37 - Starting notification fetch
2025-07-31 14:17:37 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:17:37 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:17:37 - Found 0 notifications
2025-07-31 08:17:46 - Starting notification fetch
2025-07-31 14:17:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:17:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:17:46 - Found 0 notifications
2025-07-31 08:17:47 - Starting notification fetch
2025-07-31 14:17:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:17:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:17:47 - Found 0 notifications
2025-07-31 08:17:49 - Starting notification fetch
2025-07-31 14:17:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:17:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:17:49 - Found 0 notifications
2025-07-31 08:17:54 - Starting notification fetch
2025-07-31 14:17:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:17:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:17:54 - Found 0 notifications
2025-07-31 08:18:02 - Starting notification fetch
2025-07-31 14:18:02 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:18:02 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:18:02 - Found 0 notifications
2025-07-31 08:18:07 - Starting notification fetch
2025-07-31 14:18:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:18:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:18:07 - Found 0 notifications
2025-07-31 08:18:16 - Starting notification fetch
2025-07-31 14:18:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:18:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:18:16 - Found 0 notifications
2025-07-31 08:18:17 - Starting notification fetch
2025-07-31 14:18:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:18:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:18:17 - Found 0 notifications
2025-07-31 08:18:19 - Starting notification fetch
2025-07-31 14:18:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:18:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:18:19 - Found 0 notifications
2025-07-31 08:18:24 - Starting notification fetch
2025-07-31 14:18:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:18:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:18:24 - Found 0 notifications
2025-07-31 08:18:32 - Starting notification fetch
2025-07-31 14:18:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:18:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:18:32 - Found 0 notifications
2025-07-31 08:18:37 - Starting notification fetch
2025-07-31 14:18:37 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:18:37 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:18:37 - Found 0 notifications
2025-07-31 08:18:46 - Starting notification fetch
2025-07-31 14:18:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:18:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:18:46 - Found 0 notifications
2025-07-31 08:18:47 - Starting notification fetch
2025-07-31 14:18:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:18:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:18:47 - Found 0 notifications
2025-07-31 08:18:49 - Starting notification fetch
2025-07-31 14:18:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:18:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:18:49 - Found 0 notifications
2025-07-31 08:18:54 - Starting notification fetch
2025-07-31 14:18:54 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:18:54 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:18:54 - Found 0 notifications
2025-07-31 08:19:02 - Starting notification fetch
2025-07-31 14:19:02 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:19:02 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:19:02 - Found 0 notifications
2025-07-31 08:19:07 - Starting notification fetch
2025-07-31 14:19:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:19:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:19:07 - Found 0 notifications
2025-07-31 08:19:17 - Starting notification fetch
2025-07-31 14:19:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:19:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:19:17 - Found 0 notifications
2025-07-31 08:19:17 - Starting notification fetch
2025-07-31 14:19:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:19:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:19:17 - Found 0 notifications
2025-07-31 08:19:19 - Starting notification fetch
2025-07-31 14:19:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:19:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:19:19 - Found 0 notifications
2025-07-31 08:19:25 - Starting notification fetch
2025-07-31 14:19:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:19:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:19:25 - Found 0 notifications
2025-07-31 08:19:32 - Starting notification fetch
2025-07-31 14:19:32 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:19:32 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:19:32 - Found 0 notifications
2025-07-31 08:19:37 - Starting notification fetch
2025-07-31 14:19:37 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:19:37 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:19:37 - Found 0 notifications
2025-07-31 08:19:46 - Starting notification fetch
2025-07-31 14:19:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:19:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:19:47 - Found 0 notifications
2025-07-31 08:19:49 - Starting notification fetch
2025-07-31 14:19:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:19:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:19:49 - Found 0 notifications
2025-07-31 08:19:55 - Starting notification fetch
2025-07-31 14:19:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:19:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:19:55 - Found 0 notifications
2025-07-31 08:20:06 - Starting notification fetch
2025-07-31 14:20:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:20:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:20:06 - Found 0 notifications
2025-07-31 08:20:07 - Starting notification fetch
2025-07-31 14:20:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:20:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:20:07 - Found 0 notifications
2025-07-31 08:20:15 - Starting notification fetch
2025-07-31 14:20:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:20:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:20:16 - Found 0 notifications
2025-07-31 08:20:17 - Starting notification fetch
2025-07-31 14:20:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:20:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:20:17 - Found 0 notifications
2025-07-31 08:20:19 - Starting notification fetch
2025-07-31 14:20:20 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:20:20 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:20:20 - Found 0 notifications
2025-07-31 08:20:25 - Starting notification fetch
2025-07-31 14:20:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:20:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:20:25 - Found 0 notifications
2025-07-31 08:20:36 - Starting notification fetch
2025-07-31 14:20:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:20:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:20:36 - Found 0 notifications
2025-07-31 08:20:37 - Starting notification fetch
2025-07-31 14:20:37 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:20:37 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:20:37 - Found 0 notifications
2025-07-31 08:20:45 - Starting notification fetch
2025-07-31 14:20:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:20:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:20:46 - Found 0 notifications
2025-07-31 08:20:47 - Starting notification fetch
2025-07-31 14:20:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:20:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:20:47 - Found 0 notifications
2025-07-31 08:20:49 - Starting notification fetch
2025-07-31 14:20:50 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:20:50 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:20:50 - Found 0 notifications
2025-07-31 08:20:55 - Starting notification fetch
2025-07-31 14:20:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:20:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:20:55 - Found 0 notifications
2025-07-31 08:21:06 - Starting notification fetch
2025-07-31 14:21:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:06 - Found 0 notifications
2025-07-31 08:21:07 - Starting notification fetch
2025-07-31 14:21:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:07 - Found 0 notifications
2025-07-31 08:21:15 - Starting notification fetch
2025-07-31 14:21:15 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:15 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:15 - Found 0 notifications
2025-07-31 08:21:16 - Starting notification fetch
2025-07-31 14:21:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:16 - Found 0 notifications
2025-07-31 08:21:18 - Starting notification fetch
2025-07-31 14:21:18 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:18 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:18 - Found 0 notifications
2025-07-31 08:21:18 - Starting notification fetch
2025-07-31 14:21:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:19 - Found 0 notifications
2025-07-31 08:21:24 - Starting notification fetch
2025-07-31 14:21:24 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:24 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:24 - Found 0 notifications
2025-07-31 08:21:35 - Starting notification fetch
2025-07-31 14:21:35 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:35 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:35 - Found 0 notifications
2025-07-31 08:21:36 - Starting notification fetch
2025-07-31 14:21:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:36 - Found 0 notifications
2025-07-31 08:21:46 - Starting notification fetch
2025-07-31 14:21:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:46 - Found 0 notifications
2025-07-31 08:21:46 - Starting notification fetch
2025-07-31 14:21:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:46 - Found 0 notifications
2025-07-31 08:21:48 - Starting notification fetch
2025-07-31 14:21:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:49 - Found 0 notifications
2025-07-31 08:21:55 - Starting notification fetch
2025-07-31 14:21:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:55 - Found 0 notifications
2025-07-31 08:21:57 - Starting notification fetch
2025-07-31 14:21:57 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:21:57 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:21:57 - Found 0 notifications
2025-07-31 08:22:05 - Starting notification fetch
2025-07-31 14:22:05 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:22:05 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:22:05 - Found 0 notifications
2025-07-31 08:22:06 - Starting notification fetch
2025-07-31 14:22:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:22:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:22:06 - Found 0 notifications
2025-07-31 08:22:16 - Starting notification fetch
2025-07-31 14:22:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:22:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:22:16 - Found 0 notifications
2025-07-31 08:22:16 - Starting notification fetch
2025-07-31 14:22:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:22:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:22:16 - Found 0 notifications
2025-07-31 08:22:18 - Starting notification fetch
2025-07-31 14:22:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:22:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:22:19 - Found 0 notifications
2025-07-31 08:22:25 - Starting notification fetch
2025-07-31 14:22:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:22:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:22:25 - Found 0 notifications
2025-07-31 08:22:30 - Starting notification fetch
2025-07-31 14:22:30 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:22:30 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:22:30 - Found 0 notifications
2025-07-31 08:22:35 - Starting notification fetch
2025-07-31 14:22:35 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:22:35 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:22:35 - Found 0 notifications
2025-07-31 08:22:36 - Starting notification fetch
2025-07-31 14:22:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:22:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:22:36 - Found 0 notifications
2025-07-31 08:22:46 - Starting notification fetch
2025-07-31 14:22:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:22:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:22:46 - Found 0 notifications
2025-07-31 08:22:46 - Starting notification fetch
2025-07-31 14:22:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:22:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:22:46 - Found 0 notifications
2025-07-31 08:22:48 - Starting notification fetch
2025-07-31 14:22:48 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:22:48 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:22:48 - Found 0 notifications
2025-07-31 08:22:55 - Starting notification fetch
2025-07-31 14:22:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:22:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:22:55 - Found 0 notifications
2025-07-31 08:23:00 - Starting notification fetch
2025-07-31 14:23:00 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:00 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:00 - Found 0 notifications
2025-07-31 08:23:05 - Starting notification fetch
2025-07-31 14:23:05 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:05 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:05 - Found 0 notifications
2025-07-31 08:23:06 - Starting notification fetch
2025-07-31 14:23:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:06 - Found 0 notifications
2025-07-31 08:23:16 - Starting notification fetch
2025-07-31 14:23:16 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:16 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:16 - Found 0 notifications
2025-07-31 08:23:17 - Starting notification fetch
2025-07-31 14:23:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:17 - Found 0 notifications
2025-07-31 08:23:18 - Starting notification fetch
2025-07-31 14:23:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:19 - Found 0 notifications
2025-07-31 08:23:25 - Starting notification fetch
2025-07-31 14:23:25 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:25 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:25 - Found 0 notifications
2025-07-31 08:23:30 - Starting notification fetch
2025-07-31 14:23:30 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:30 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:30 - Found 0 notifications
2025-07-31 08:23:35 - Starting notification fetch
2025-07-31 14:23:35 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:35 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:35 - Found 0 notifications
2025-07-31 08:23:36 - Starting notification fetch
2025-07-31 14:23:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:36 - Found 0 notifications
2025-07-31 08:23:46 - Starting notification fetch
2025-07-31 14:23:46 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:46 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:46 - Found 0 notifications
2025-07-31 08:23:47 - Starting notification fetch
2025-07-31 14:23:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:47 - Found 0 notifications
2025-07-31 08:23:48 - Starting notification fetch
2025-07-31 14:23:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:49 - Found 0 notifications
2025-07-31 08:23:55 - Starting notification fetch
2025-07-31 14:23:55 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:23:55 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:23:55 - Found 0 notifications
2025-07-31 08:24:05 - Starting notification fetch
2025-07-31 14:24:05 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:24:05 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:24:05 - Found 0 notifications
2025-07-31 08:24:06 - Starting notification fetch
2025-07-31 14:24:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:24:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:24:06 - Found 0 notifications
2025-07-31 08:24:17 - Starting notification fetch
2025-07-31 14:24:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:24:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:24:17 - Found 0 notifications
2025-07-31 08:24:17 - Starting notification fetch
2025-07-31 14:24:17 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:24:17 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:24:17 - Found 0 notifications
2025-07-31 08:24:18 - Starting notification fetch
2025-07-31 14:24:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:24:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:24:19 - Found 0 notifications
2025-07-31 08:24:26 - Starting notification fetch
2025-07-31 14:24:26 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:24:26 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:24:26 - Found 0 notifications
2025-07-31 08:24:35 - Starting notification fetch
2025-07-31 14:24:35 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:24:35 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:24:35 - Found 0 notifications
2025-07-31 08:24:36 - Starting notification fetch
2025-07-31 14:24:37 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:24:37 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:24:37 - Found 0 notifications
2025-07-31 08:24:47 - Starting notification fetch
2025-07-31 14:24:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:24:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:24:47 - Found 0 notifications
2025-07-31 08:24:47 - Starting notification fetch
2025-07-31 14:24:47 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:24:47 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:24:47 - Found 0 notifications
2025-07-31 08:24:48 - Starting notification fetch
2025-07-31 14:24:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:24:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:24:49 - Found 0 notifications
2025-07-31 08:24:56 - Starting notification fetch
2025-07-31 14:24:56 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:24:56 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:24:56 - Found 0 notifications
2025-07-31 08:25:05 - Starting notification fetch
2025-07-31 14:25:05 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:25:05 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:25:05 - Found 0 notifications
2025-07-31 08:25:07 - Starting notification fetch
2025-07-31 14:25:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:25:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:25:07 - Found 0 notifications
2025-07-31 08:25:18 - Starting notification fetch
2025-07-31 08:25:18 - Starting notification fetch
2025-07-31 14:25:18 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:25:18 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:25:18 - Found 0 notifications
2025-07-31 14:25:18 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:25:18 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:25:18 - Found 0 notifications
2025-07-31 08:25:18 - Starting notification fetch
2025-07-31 14:25:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:25:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:25:19 - Found 0 notifications
2025-07-31 08:25:26 - Starting notification fetch
2025-07-31 14:25:26 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:25:26 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:25:26 - Found 0 notifications
2025-07-31 08:25:27 - Starting notification fetch
2025-07-31 14:25:27 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:25:27 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:25:27 - Found 0 notifications
2025-07-31 08:25:35 - Starting notification fetch
2025-07-31 14:25:35 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:25:35 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:25:35 - Found 0 notifications
2025-07-31 08:25:37 - Starting notification fetch
2025-07-31 14:25:37 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:25:37 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:25:37 - Found 0 notifications
2025-07-31 08:25:40 - Starting notification fetch
2025-07-31 14:25:40 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:25:40 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:25:40 - Found 0 notifications
2025-07-31 08:25:48 - Starting notification fetch
2025-07-31 14:25:48 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:25:48 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:25:48 - Found 0 notifications
2025-07-31 08:25:48 - Starting notification fetch
2025-07-31 14:25:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:25:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:25:49 - Found 0 notifications
2025-07-31 08:25:57 - Starting notification fetch
2025-07-31 14:25:57 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:25:57 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:25:57 - Found 0 notifications
2025-07-31 08:26:02 - Starting notification fetch
2025-07-31 14:26:02 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:26:02 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:26:02 - Found 0 notifications
2025-07-31 08:26:05 - Starting notification fetch
2025-07-31 14:26:05 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:26:05 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:26:05 - Found 0 notifications
2025-07-31 08:26:07 - Starting notification fetch
2025-07-31 14:26:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:26:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:26:07 - Found 0 notifications
2025-07-31 08:26:19 - Starting notification fetch
2025-07-31 14:26:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:26:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:26:19 - Found 0 notifications
2025-07-31 08:26:19 - Starting notification fetch
2025-07-31 14:26:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:26:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:26:19 - Found 0 notifications
2025-07-31 08:26:27 - Starting notification fetch
2025-07-31 14:26:28 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:26:28 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:26:28 - Found 0 notifications
2025-07-31 08:26:33 - Starting notification fetch
2025-07-31 14:26:33 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:26:33 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:26:33 - Found 0 notifications
2025-07-31 08:26:36 - Starting notification fetch
2025-07-31 14:26:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:26:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:26:36 - Found 0 notifications
2025-07-31 08:26:37 - Starting notification fetch
2025-07-31 14:26:37 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:26:37 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:26:37 - Found 0 notifications
2025-07-31 08:26:49 - Starting notification fetch
2025-07-31 14:26:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:26:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:26:49 - Found 0 notifications
2025-07-31 08:26:49 - Starting notification fetch
2025-07-31 14:26:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:26:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:26:49 - Found 0 notifications
2025-07-31 08:26:57 - Starting notification fetch
2025-07-31 14:26:58 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:26:58 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:26:58 - Found 0 notifications
2025-07-31 08:27:03 - Starting notification fetch
2025-07-31 14:27:03 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:27:03 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:27:03 - Found 0 notifications
2025-07-31 08:27:06 - Starting notification fetch
2025-07-31 14:27:06 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:27:06 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:27:06 - Found 0 notifications
2025-07-31 08:27:07 - Starting notification fetch
2025-07-31 14:27:08 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:27:08 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:27:08 - Found 0 notifications
2025-07-31 08:27:19 - Starting notification fetch
2025-07-31 14:27:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:27:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:27:19 - Found 0 notifications
2025-07-31 08:27:19 - Starting notification fetch
2025-07-31 14:27:19 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:27:19 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:27:19 - Found 0 notifications
2025-07-31 08:27:27 - Starting notification fetch
2025-07-31 14:27:27 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:27:27 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:27:27 - Found 0 notifications
2025-07-31 08:27:33 - Starting notification fetch
2025-07-31 14:27:33 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:27:33 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:27:33 - Found 0 notifications
2025-07-31 08:27:36 - Starting notification fetch
2025-07-31 14:27:36 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:27:36 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:27:36 - Found 0 notifications
2025-07-31 08:27:37 - Starting notification fetch
2025-07-31 14:27:38 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:27:38 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:27:38 - Found 0 notifications
2025-07-31 08:27:49 - Starting notification fetch
2025-07-31 14:27:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:27:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:27:49 - Found 0 notifications
2025-07-31 08:27:49 - Starting notification fetch
2025-07-31 08:27:49 - Starting notification fetch
2025-07-31 14:27:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:27:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:27:49 - Found 0 notifications
2025-07-31 14:27:49 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:27:49 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:27:49 - Found 0 notifications
2025-07-31 08:27:58 - Starting notification fetch
2025-07-31 14:27:58 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:27:58 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:27:58 - Found 0 notifications
2025-07-31 08:28:04 - Starting notification fetch
2025-07-31 14:28:04 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:28:04 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:28:04 - Found 0 notifications
2025-07-31 08:28:07 - Starting notification fetch
2025-07-31 14:28:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:28:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:28:07 - Found 0 notifications
2025-07-31 08:28:07 - Starting notification fetch
2025-07-31 14:28:07 - SQL Query: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20
2025-07-31 14:28:07 - SQL: SELECT notification_id as id, message, type, reference_id, created_at, is_read, user_id
                FROM notifications
                WHERE user_id = ? AND is_read = 0
                ORDER BY created_at DESC
                LIMIT 20 with user_id = 1
2025-07-31 14:28:07 - Found 0 notifications
